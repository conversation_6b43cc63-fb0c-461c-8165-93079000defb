package vn.com.mbbank.kanban.mbmonitor.common.utils;

import java.util.Map;
import java.util.Objects;
import org.springframework.data.jpa.repository.query.EscapeCharacter;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/7/2025
 */
public class SqlUtils {

  /**
   * Build query page.
   *
   * @param page              page
   * @param mapColumns        mapping from column FE and Database example: oderBy name Map name- service.name
   * @param querySearchCustom add custom query search example : ( select * from other where a like :search )
   * @param isExactSearch     isExactSearch
   * @return Sql query
   */
  public static String buildPageQuery(PaginationRequestDTO page, Map<String, String> mapColumns,
                                      String querySearchCustom, Boolean isExactSearch) {
    // build search
    StringBuilder query = new StringBuilder();
    // build search
    if (!KanbanCommonUtil.isEmpty(page.getSearch())) {
      for (var column : mapColumns.values()) {
        query.append(query.isEmpty() ? " AND ( " : " OR ");
        query.append(
            String.format(isExactSearch ? "%s LIKE :search ESCAPE '\\'" :
                "LOWER(%s) LIKE :search ESCAPE '\\' ", column));
      }
    }
    if (!KanbanCommonUtil.isEmpty(querySearchCustom)) {
      query.append(query.isEmpty() ? " AND (" : " OR ");
      query.append(querySearchCustom);
    }

    if (!KanbanCommonUtil.isEmpty(page.getSearch())
        ||
        !KanbanCommonUtil.isEmpty(querySearchCustom)) {
      query.append(")");
    }

    // append order
    if (!StringUtils.isNullOrEmpty(page.getSortBy())) {
      query.append(" ORDER BY " + mapColumns.get(page.getSortBy()));
      if (Objects.nonNull(page.getSortOrder())) {
        query.append(" ").append(page.getSortOrder().name());
      }
    }
    return query.toString();
  }

  /**
   * Build query page.
   *
   * @param page       page
   * @param mapColumns mapping from column FE and Database example: oderBy name Map name- service.name
   * @return Sql query
   */
  public static String buildPageQuery(PaginationRequestDTO page, Map<String, String> mapColumns) {
    return buildPageQuery(page, mapColumns, null, false);

  }

  /**
   * Build query page.
   *
   * @param page              page
   * @param mapColumns        mapping from column FE and Database example: oderBy name Map name- service.name
   * @param querySearchCustom add custom query search example : ( select * from other where a like :search )
   * @return Sql query
   */

  public static String buildPageQuery(PaginationRequestDTO page, Map<String, String> mapColumns,
                                      String querySearchCustom) {
    return buildPageQuery(page, mapColumns, querySearchCustom, false);

  }

  /**
   * Escape search oracle.
   *
   * @param keyword data search
   * @return data after Escape
   */
  public static String getSearchEscape(String keyword) {
    if (keyword == null) {
      return "%%";
    }

    return "%"
        +
        EscapeCharacter.DEFAULT.escape(keyword)
        +
        "%";
  }

}
