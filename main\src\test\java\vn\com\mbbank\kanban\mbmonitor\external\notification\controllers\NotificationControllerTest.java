package vn.com.mbbank.kanban.mbmonitor.external.notification.controllers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;
import vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request.NotificationCursor;
import vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request.NotificationSearchRequest;
import vn.com.mbbank.kanban.mbmonitor.external.notification.services.NotificationService;

/**
 * Unit tests for NotificationController.
 */
@ExtendWith(MockitoExtension.class)
class NotificationControllerTest {

  @Mock
  private NotificationService notificationService;

  @Mock
  private CommonAclPermissionService commonAclPermissionService;

  @InjectMocks
  private NotificationController notificationController;

  @Test
  void save_success_withValidRequest() {
    // Arrange
    NotificationRequest request = NotificationRequest.builder()
        .title("Test Notification")
        .content("Test Content")
        .type(NotificationTypeEnum.INFO)
        .userName("testuser")
        .sourceId("source123")
        .sourceType(NotificationSourceTypeEnum.ALERT)
        .build();

    NotificationResponse expectedResponse = NotificationResponse.builder()
        .id("notification123")
        .title("Test Notification")
        .content("Test Content")
        .type(NotificationTypeEnum.INFO)
        .userName("testuser")
        .sourceId("source123")
        .sourceType(NotificationSourceTypeEnum.ALERT)
        .isRead(false)
        .createdDate("2024-01-01T10:00:00")
        .build();

    when(notificationService.save(request)).thenReturn(expectedResponse);

    // Act
    ResponseData<NotificationResponse> result = notificationController.save(request);

    // Assert
    assertNotNull(result);
    assertNotNull(result.getData());
    assertEquals(expectedResponse.getId(), result.getData().getId());
    assertEquals(expectedResponse.getTitle(), result.getData().getTitle());
    assertEquals(expectedResponse.getContent(), result.getData().getContent());
    assertEquals(expectedResponse.getType(), result.getData().getType());
    assertEquals(expectedResponse.getUserName(), result.getData().getUserName());
    assertEquals(expectedResponse.getSourceId(), result.getData().getSourceId());
    assertEquals(expectedResponse.getSourceType(), result.getData().getSourceType());
    assertEquals(expectedResponse.getIsRead(), result.getData().getIsRead());
    assertEquals(expectedResponse.getCreatedDate(), result.getData().getCreatedDate());

    verify(notificationService).save(request);
  }

  @Test
  void getUnreadCount_success_withAuthenticatedUser() {
    // Arrange
    String userId = "testuser";
    Long expectedCount = 5L;

    // Mock the getUserId() method from BaseController
    NotificationController spyController = new NotificationController(notificationService) {
      @Override
      public String getUserId() {
        return userId;
      }
    };

    when(notificationService.getUnreadCount(userId)).thenReturn(expectedCount);

    // Act
    ResponseData<Long> result = spyController.getUnreadCount();

    // Assert
    assertNotNull(result);
    assertNotNull(result.getData());
    assertEquals(expectedCount, result.getData());

    verify(notificationService).getUnreadCount(userId);
  }

  @Test
  void markAsRead_success_withValidId() throws BusinessException {
    // Arrange
    String notificationId = "notification123";

    doNothing().when(notificationService).markAsRead(notificationId);

    // Act
    ResponseData<String> result = notificationController.markAsRead(notificationId);

    // Assert
    assertNotNull(result);
    assertNotNull(result.getData());
    assertEquals("OK", result.getData());

    verify(notificationService).markAsRead(notificationId);
  }

  @Test
  void markAsRead_failed_whenNotificationNotFound() throws BusinessException {
    // Arrange
    String notificationId = "nonexistent123";

    doThrow(new BusinessException(ErrorCode.NOTIFICATION_NOT_FOUND))
        .when(notificationService).markAsRead(notificationId);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      notificationController.markAsRead(notificationId);
    });

    assertEquals(ErrorCode.NOTIFICATION_NOT_FOUND.getCode(), exception.getCode());

    verify(notificationService).markAsRead(notificationId);
  }

  @Test
  void findAll_success_withSearchRequest() {
    // Arrange
    NotificationSearchRequest searchRequest = NotificationSearchRequest.builder()
        .pageSize(10)
        .search("test")
        .sourceTypes(Arrays.asList(NotificationSourceTypeEnum.ALERT))
        .isRead(false)
        .lastId("last123")
        .lastCreatedDate("2024-01-01T09:00:00")
        .build();

    List<NotificationResponse> notifications = Arrays.asList(
        NotificationResponse.builder()
            .id("notification1")
            .title("Notification 1")
            .content("Content 1")
            .type(NotificationTypeEnum.INFO)
            .userName("testuser")
            .sourceId("source1")
            .sourceType(NotificationSourceTypeEnum.ALERT)
            .isRead(false)
            .createdDate("2024-01-01T10:00:00")
            .build(),
        NotificationResponse.builder()
            .id("notification2")
            .title("Notification 2")
            .content("Content 2")
            .type(NotificationTypeEnum.WARNING)
            .userName("testuser")
            .sourceId("source2")
            .sourceType(NotificationSourceTypeEnum.ALERT)
            .isRead(false)
            .createdDate("2024-01-01T11:00:00")
            .build()
    );

    NotificationCursor nextCursor = NotificationCursor.builder()
        .lastId("notification2")
        .lastCreatedDate("2024-01-01T11:00:00")
        .build();

    CursorPageResponse<NotificationResponse, NotificationCursor> expectedResponse =
        CursorPageResponse.<NotificationResponse, NotificationCursor>builder()
            .data(notifications)
            .nextCursor(nextCursor)
            .build();

    when(notificationService.findAllWithPaging(searchRequest)).thenReturn(expectedResponse);

    // Act
    ResponseData<CursorPageResponse<NotificationResponse, NotificationCursor>> result =
        notificationController.findAll(searchRequest);

    // Assert
    assertNotNull(result);
    assertNotNull(result.getData());
    assertSame(expectedResponse, result.getData());
    assertEquals(2, result.getData().getData().size());
    assertEquals("notification1", result.getData().getData().get(0).getId());
    assertEquals("notification2", result.getData().getData().get(1).getId());
    assertNotNull(result.getData().getNextCursor());
    assertEquals("notification2", result.getData().getNextCursor().getLastId());
    assertEquals("2024-01-01T11:00:00", result.getData().getNextCursor().getLastCreatedDate());

    verify(notificationService).findAllWithPaging(searchRequest);
  }

  @Test
  void findAll_success_withEmptyResults() {
    // Arrange
    NotificationSearchRequest searchRequest = NotificationSearchRequest.builder()
        .pageSize(10)
        .search("nonexistent")
        .isRead(true)
        .build();

    CursorPageResponse<NotificationResponse, NotificationCursor> expectedResponse =
        CursorPageResponse.<NotificationResponse, NotificationCursor>builder()
            .data(Collections.emptyList())
            .nextCursor(null)
            .build();

    when(notificationService.findAllWithPaging(searchRequest)).thenReturn(expectedResponse);

    // Act
    ResponseData<CursorPageResponse<NotificationResponse, NotificationCursor>> result =
        notificationController.findAll(searchRequest);

    // Assert
    assertNotNull(result);
    assertNotNull(result.getData());
    assertSame(expectedResponse, result.getData());
    assertEquals(0, result.getData().getData().size());
    assertEquals(null, result.getData().getNextCursor());

    verify(notificationService).findAllWithPaging(searchRequest);
  }

  @Test
  void save_success_withMinimalRequest() {
    // Arrange
    NotificationRequest request = NotificationRequest.builder()
        .title("Minimal Notification")
        .userName("user123")
        .build();

    NotificationResponse expectedResponse = NotificationResponse.builder()
        .id("notification456")
        .title("Minimal Notification")
        .userName("user123")
        .isRead(false)
        .createdDate("2024-01-01T12:00:00")
        .build();

    when(notificationService.save(request)).thenReturn(expectedResponse);

    // Act
    ResponseData<NotificationResponse> result = notificationController.save(request);

    // Assert
    assertNotNull(result);
    assertNotNull(result.getData());
    assertEquals(expectedResponse.getId(), result.getData().getId());
    assertEquals(expectedResponse.getTitle(), result.getData().getTitle());
    assertEquals(expectedResponse.getUserName(), result.getData().getUserName());
    assertEquals(expectedResponse.getIsRead(), result.getData().getIsRead());

    verify(notificationService).save(request);
  }

  @Test
  void getUnreadCount_success_withZeroCount() {
    // Arrange
    String userId = "userWithNoUnread";
    Long expectedCount = 0L;

    // Mock the getUserId() method from BaseController
    NotificationController spyController = new NotificationController(notificationService) {
      @Override
      public String getUserId() {
        return userId;
      }
    };

    when(notificationService.getUnreadCount(userId)).thenReturn(expectedCount);

    // Act
    ResponseData<Long> result = spyController.getUnreadCount();

    // Assert
    assertNotNull(result);
    assertNotNull(result.getData());
    assertEquals(expectedCount, result.getData());

    verify(notificationService).getUnreadCount(userId);
  }

  @Test
  void findAll_success_withDefaultSearchRequest() {
    // Arrange
    NotificationSearchRequest searchRequest = new NotificationSearchRequest();

    List<NotificationResponse> notifications = Arrays.asList(
        NotificationResponse.builder()
            .id("notification3")
            .title("Default Search Result")
            .content("Default Content")
            .type(NotificationTypeEnum.ERROR)
            .userName("defaultuser")
            .isRead(true)
            .createdDate("2024-01-01T13:00:00")
            .build()
    );

    CursorPageResponse<NotificationResponse, NotificationCursor> expectedResponse =
        CursorPageResponse.<NotificationResponse, NotificationCursor>builder()
            .data(notifications)
            .nextCursor(null)
            .build();

    when(notificationService.findAllWithPaging(searchRequest)).thenReturn(expectedResponse);

    // Act
    ResponseData<CursorPageResponse<NotificationResponse, NotificationCursor>> result =
        notificationController.findAll(searchRequest);

    // Assert
    assertNotNull(result);
    assertNotNull(result.getData());
    assertEquals(1, result.getData().getData().size());
    assertEquals("notification3", result.getData().getData().get(0).getId());
    assertEquals("Default Search Result", result.getData().getData().get(0).getTitle());
    assertEquals(null, result.getData().getNextCursor());

    verify(notificationService).findAllWithPaging(searchRequest);
  }

  @Test
  void markAsRead_success_withNullId() throws BusinessException {
    // Arrange
    String notificationId = null;

    doNothing().when(notificationService).markAsRead(notificationId);

    // Act
    ResponseData<String> result = notificationController.markAsRead(notificationId);

    // Assert
    assertNotNull(result);
    assertNotNull(result.getData());
    assertEquals("OK", result.getData());

    verify(notificationService).markAsRead(notificationId);
  }

  @Test
  void markAsRead_success_withEmptyId() throws BusinessException {
    // Arrange
    String notificationId = "";

    doNothing().when(notificationService).markAsRead(notificationId);

    // Act
    ResponseData<String> result = notificationController.markAsRead(notificationId);

    // Assert
    assertNotNull(result);
    assertNotNull(result.getData());
    assertEquals("OK", result.getData());

    verify(notificationService).markAsRead(notificationId);
  }
}
