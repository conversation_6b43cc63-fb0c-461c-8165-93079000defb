package vn.com.mbbank.kanban.mbmonitor.external.notification.configs.redis;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import vn.com.mbbank.kanban.mbmonitor.external.notification.constans.RedisConstants;

/**
 * Configuration class responsible for setting up Redis Pub/Sub functionality within the application.
 * This class defines the necessary beans to enable communication between components using
 * Redis messaging mechanisms, including topics and message listeners.
 */
@Configuration
public class RedisPubSubConfig {

  /**
   * Defines a Redis ChannelTopic bean for notifications.
   *
   * @return a ChannelTopic object representing the notification topic used
   *         for Redis Pub/Sub messaging.
   */
  @Bean
  public ChannelTopic notificationTopic() {
    return new ChannelTopic(RedisConstants.NOTIFICATION_REDIS_TOPIC);
  }

  /**
   * Creates and configures a RedisMessageListenerContainer to listen for
   * messages on the notification topic. Registers a given message listener
   * to handle notifications.
   *
   * @param connectionFactory the RedisConnectionFactory used to establish
   *                          connections to the Redis server.
   * @param notificationRedisSubscriber the listener for processing messages
   *                                    received on the notification topic.
   * @return a configured RedisMessageListenerContainer capable of handling
   *         messages on the notification topic.
   */
  @Bean
  public RedisMessageListenerContainer notificationTopicListenerContainer(
      RedisConnectionFactory connectionFactory,
      NotificationRedisSubscriber notificationRedisSubscriber) {

    RedisMessageListenerContainer container = new RedisMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);

    container.addMessageListener(notificationRedisSubscriber, notificationTopic());

    return container;
  }
}