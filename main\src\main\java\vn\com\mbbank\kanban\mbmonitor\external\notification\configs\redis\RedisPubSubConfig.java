package vn.com.mbbank.kanban.mbmonitor.external.notification.configs.redis;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import vn.com.mbbank.kanban.mbmonitor.external.notification.constans.RedisConstants;

@Configuration
public class RedisPubSubConfig {

  @Bean
  public ChannelTopic notificationTopic() {
    return new ChannelTopic(RedisConstants.NOTIFICATION_REDIS_TOPIC);
  }

  @Bean
  public RedisMessageListenerContainer notificationTopicListenerContainer(
      RedisConnectionFactory connectionFactory,
      NotificationRedisSubscriber notificationRedisSubscriber) {

    RedisMessageListenerContainer container = new RedisMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);

    container.addMessageListener(notificationRedisSubscriber, notificationTopic());

    return container;
  }
}