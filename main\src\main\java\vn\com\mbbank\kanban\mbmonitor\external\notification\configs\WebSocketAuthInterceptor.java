package vn.com.mbbank.kanban.mbmonitor.external.notification.configs;

import java.security.PublicKey;
import java.security.interfaces.RSAPublicKey;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.introspection.OpaqueTokenIntrospector;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.configs.configurations.AuthenticationLocalConfig;
import vn.com.mbbank.kanban.core.utils.KanbanTokenUtils;

@Slf4j
@Component
@RequiredArgsConstructor
public class WebSocketAuthInterceptor implements ChannelInterceptor {

  private final OpaqueTokenIntrospector opaqueTokenIntrospector;
  private final AuthenticationLocalConfig authenticationLocalConfig;

  @Override
  public Message<?> preSend(Message<?> message, MessageChannel channel) {
    StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
    if (accessor == null || accessor.getCommand() == null) {
      return message;
    }

    if (StompCommand.CONNECT.equals(accessor.getCommand())) {
      String authorizationHeader = accessor.getFirstNativeHeader("Authorization");

      if (authorizationHeader == null || !authorizationHeader.startsWith("Bearer ")) {
        throw new IllegalArgumentException("Authorization header missing or not Bearer type");
      }

      String token = authorizationHeader.replaceFirst("Bearer", "").trim();
      Optional<UsernamePasswordAuthenticationToken> authentication = authenticateToken(token);

      if (authentication.isPresent()) {
        UsernamePasswordAuthenticationToken auth = authentication.get();
        SecurityContextHolder.getContext().setAuthentication(auth);
        accessor.setUser(auth); // Associate user with WebSocket session
      } else {
        throw new SecurityException("Invalid or expired token");
      }
    }

    return message;
  }

  private Optional<UsernamePasswordAuthenticationToken> authenticateToken(String token) {
    // Try local authentication first
    if (authenticationLocalConfig.getEnable()) {
      try {
        Optional<UsernamePasswordAuthenticationToken> localAuth = authenticateWithLocalKey(token);
        if (localAuth.isPresent()) {
          return localAuth;
        } else {
          log.info("Local token validation failed, falling back to Keycloak introspection.");
        }
      } catch (Exception e) {
        log.warn("Local authentication failed: {}", e.getMessage());
      }
    }

    // Fallback to Keycloak introspection
    try {
      return authenticateWithKeycloak(token);
    } catch (Exception e) {
      log.warn("Keycloak authentication failed: {}", e.getMessage());
    }

    return Optional.empty();
  }

  private Optional<UsernamePasswordAuthenticationToken> authenticateWithLocalKey(String token) throws Exception {
    PublicKey publicKey = KanbanTokenUtils.getPublicKeyFromPem(authenticationLocalConfig.getPublicKey());

    if (!KanbanTokenUtils.isTokenValid(token, publicKey)) {
      return Optional.empty();
    }

    Map<String, String> extractedClaims = KanbanTokenUtils.extractClaimsFromToken(token, (RSAPublicKey) publicKey);
    Map<String, Object> claims = new HashMap<>(extractedClaims);
    var userName = extractedClaims.get("preferred_username");
    UsernamePasswordAuthenticationToken principal =
        new UsernamePasswordAuthenticationToken(userName, claims, Collections.emptyList());
    return Optional.of(new UsernamePasswordAuthenticationToken(userName, null, principal.getAuthorities()));
  }

  private Optional<UsernamePasswordAuthenticationToken> authenticateWithKeycloak(String token) {
    var principal = opaqueTokenIntrospector.introspect(token);
    return Optional.of(new UsernamePasswordAuthenticationToken(principal, null, principal.getAuthorities()));
  }
}