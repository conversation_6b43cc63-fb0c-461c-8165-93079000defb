package vn.com.mbbank.kanban.mbmonitor.external.notification.configs;

import java.security.PublicKey;
import java.security.interfaces.RSAPublicKey;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.introspection.OpaqueTokenIntrospector;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.configs.configurations.AuthenticationLocalConfig;
import vn.com.mbbank.kanban.core.configs.configurations.KeycloakCentrailizedConfig;
import vn.com.mbbank.kanban.core.utils.KanbanTokenUtils;

@Slf4j
@Component
@RequiredArgsConstructor
public class WebSocketAuthInterceptor implements ChannelInterceptor {

  private final OpaqueTokenIntrospector opaqueTokenIntrospector;
  private final AuthenticationLocalConfig authenticationLocalConfig;
  private final KeycloakCentrailizedConfig keycloakCentrailizedConfig;

  @Override
  public Message<?> preSend(Message<?> message, MessageChannel channel) {
    StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);

    if (accessor == null || accessor.getCommand() == null) {
      return message;
    }

    if (StompCommand.CONNECT.equals(accessor.getCommand())) {
      handleAuthentication(accessor);
    }

    return message;
  }

  private void handleAuthentication(StompHeaderAccessor accessor) {
    String authHeader = accessor.getFirstNativeHeader("Authorization");

    if (authHeader == null || !authHeader.startsWith("Bearer ")) {
      throw new IllegalArgumentException("Authorization header missing or not Bearer type");
    }

    String token = extractToken(authHeader);

    Optional<UsernamePasswordAuthenticationToken> authentication = authenticateToken(token);

    authentication.ifPresentOrElse(auth -> {
      SecurityContextHolder.getContext().setAuthentication(auth);
      accessor.setUser(auth);
    }, () -> {
      throw new SecurityException("Invalid or expired token");
    });
  }

  private String extractToken(String header) {
    return header.replaceFirst("Bearer", "").trim();
  }

  private Optional<UsernamePasswordAuthenticationToken> authenticateToken(String token) {
    if (authenticationLocalConfig.getEnable()) {
      Optional<UsernamePasswordAuthenticationToken> result =
          tryAuthenticate(token, authenticationLocalConfig.getPublicKey(), "Local");
      if (result.isPresent()) {
        return result;
      }
    }

    if (keycloakCentrailizedConfig.getEnable()) {
      Optional<UsernamePasswordAuthenticationToken> result =
          tryAuthenticate(token, keycloakCentrailizedConfig.getPublicKey(), "Keycloak Centralized");
      if (result.isPresent()) {
        return result;
      }
    }

    return tryKeycloakIntrospection(token);
  }

  private Optional<UsernamePasswordAuthenticationToken> tryAuthenticate(String token, String pemKey, String source) {
    try {
      PublicKey publicKey = KanbanTokenUtils.getPublicKeyFromPem(pemKey);
      if (!KanbanTokenUtils.isTokenValid(token, publicKey)) {
        return Optional.empty();
      }

      Map<String, String> extractedClaims = KanbanTokenUtils.extractClaimsFromToken(token, (RSAPublicKey) publicKey);
      String username = extractedClaims.get("preferred_username");

      Map<String, Object> claims = new HashMap<>(extractedClaims);
      var principal = new UsernamePasswordAuthenticationToken(username, claims, Collections.emptyList());

      return Optional.of(new UsernamePasswordAuthenticationToken(username, null, principal.getAuthorities()));
    } catch (Exception e) {
      log.warn("{} authentication failed: {}", source, e.getMessage());
      return Optional.empty();
    }
  }

  private Optional<UsernamePasswordAuthenticationToken> tryKeycloakIntrospection(String token) {
    try {
      var principal = opaqueTokenIntrospector.introspect(token);
      return Optional.of(new UsernamePasswordAuthenticationToken(principal, null, principal.getAuthorities()));
    } catch (Exception e) {
      log.warn("Keycloak introspection failed: {}", e.getMessage());
      return Optional.empty();
    }
  }
}
