package vn.com.mbbank.kanban.mbmonitor.external.notification.configs.redis;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import java.nio.charset.StandardCharsets;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.user.SimpUserRegistry;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationResponse;

/**
 * NotificationRedisSubscriber listens to Redis messages on a specific channel
 * and processes the received notifications for delivery to connected users.
 * Implements the MessageListener interface for handling messages from Redis Pub/Sub.
 */
@Component
@RequiredArgsConstructor
public class NotificationRedisSubscriber implements MessageListener {

  private static final Logger logger = LoggerFactory.getLogger(NotificationRedisSubscriber.class);
  private final SimpUserRegistry userRegistry;
  private final SimpMessagingTemplate messagingTemplate;

  /**
   * Handles incoming messages from a Redis Pub/Sub channel. Processes the received message payload,
   * converts it to a `NotificationResponse` object, and routes the notification to a specific user
   * if the user is actively connected.
   *
   * @param message the incoming Redis Pub/Sub message, containing the channel and payload data.
   * @param pattern the optional pattern matching the channel, typically used in Redis for subscriptions.
   */
  @Override
  public void onMessage(Message message, byte[] pattern) {
    try {
      String channel = new String(message.getChannel());
      String body = new String(message.getBody(), StandardCharsets.UTF_8);
      logger.info("Received message from topic '{}': {}", channel, body);
      // First parse as JSONObject to validate and sanitize the input
      JSONObject jsonObject = JSON.parseObject(body);
      NotificationResponse notification = jsonObject.toJavaObject(NotificationResponse.class);

      var userName = notification.getUserName();
      if (hasUserConnection(userName)) {
        messagingTemplate.convertAndSendToUser(userName, "/queue/notifications", notification);
      }
    } catch (Exception e) {
      logger.error("Error processing notification redis message: {}", e.getMessage(), e);
    }
  }

  protected boolean hasUserConnection(String username) {
    return userRegistry.getUsers().stream()
        .anyMatch(user -> user.getName().equals(username));
  }
}
