package vn.com.mbbank.kanban.mbmonitor.external.notification.configs.redis;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import java.nio.charset.StandardCharsets;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.user.SimpUserRegistry;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEntity;

@Component
@RequiredArgsConstructor
public class NotificationRedisSubscriber implements MessageListener {

  private static final Logger logger = LoggerFactory.getLogger(NotificationRedisSubscriber.class);
  private final SimpUserRegistry userRegistry;
  private final SimpMessagingTemplate messagingTemplate;

  @Override
  public void onMessage(Message message, byte[] pattern) {
    try {
      String channel = new String(message.getChannel());
      String body = new String(message.getBody(), StandardCharsets.UTF_8);
      logger.info("Received message from topic '{}': {}", channel, body);
      // First parse as JSONObject to validate and sanitize the input
      JSONObject jsonObject = JSON.parseObject(body);
      NotificationResponse notification = jsonObject.toJavaObject(NotificationResponse.class);

      var userName = notification.getUserName();
      if (hasUserConnection(userName)) {
        messagingTemplate.convertAndSendToUser(userName, "/queue/notifications", notification);
      }
    } catch (Exception e) {
      logger.error("Error processing notification redis message: " + e.getMessage(), e);
    }
  }

  public boolean hasUserConnection(String username) {
    return userRegistry.getUsers().stream()
        .anyMatch(user -> user.getName().equals(username));
  }
}
