package vn.com.mbbank.kanban.mbmonitor.external.notification.configs;

import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

/**
 * WebSocketConfig is a configuration class that enables and configures WebSocket
 * message handling for the application. This class implements the
 * WebSocketMessageBrokerConfigurer and provides customization for the message broker
 * options and the registration of STOMP endpoints.
 * This configuration enables a simple in-memory message broker, specifies application
 * destination prefixes, and defines user-specific destinations. Additionally, it
 * registers a WebSocket endpoint and configures SockJS fallback options for
 * compatibility with clients that do not support WebSocket.
 */
@Configuration
@EnableWebSocket
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

  private final WebSocketAuthInterceptor webSocketAuthInterceptor;

  public WebSocketConfig(WebSocketAuthInterceptor webSocketAuthInterceptor) {
    this.webSocketAuthInterceptor = webSocketAuthInterceptor;
  }


  /**
   * Configures the message broker for WebSocket communication by setting up the
   * destination prefixes and enabling a simple in-memory message broker.
   *
   * @param config the {@link MessageBrokerRegistry} used to configure the message broker
   *                for the application. Includes settings for enabling the broker,
   *                defining application-specific destination prefixes, and specifying
   *                user-specific prefixes.
   */
  @Override
  public void configureMessageBroker(MessageBrokerRegistry config) {
    config.enableSimpleBroker("/topic", "/queue");
    config.setUserDestinationPrefix("/user");
  }

  /**
   * Registers the STOMP (Simple Text Oriented Messaging Protocol) WebSocket endpoint
   * used for client connections. This method sets up the specified endpoint path,
   * allows cross-origin requests (CORS) using a permissive policy, and enables
   * SockJS as a fallback for clients that do not support WebSocket.
   *
   * @param registry the {@link StompEndpointRegistry} used to register the STOMP
   *                 WebSocket endpoints for the application.
   */
  @Override
  public void registerStompEndpoints(StompEndpointRegistry registry) {
    registry.addEndpoint("/ws")
        .setAllowedOriginPatterns("*")
        .withSockJS();
  }

  @Override
  public void configureClientInboundChannel(ChannelRegistration registration) {
    registration.interceptors(webSocketAuthInterceptor);
  }
}

