package vn.com.mbbank.kanban.mbmonitor.external.notification.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.notification.NotificationEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.notification.NotificationResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;
import vn.com.mbbank.kanban.mbmonitor.external.notification.configs.redis.NotificationRedisPublisher;
import vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request.NotificationCursor;
import vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request.NotificationSearchRequest;
import vn.com.mbbank.kanban.mbmonitor.external.notification.repositories.NotificationRepository;

/**
 * Unit tests for NotificationServiceImpl.
 */
@ExtendWith(MockitoExtension.class)
class NotificationServiceImplTest {

  @Mock
  private NotificationRepository notificationRepository;

  @Mock
  private NotificationRedisPublisher notificationRedisPublisher;

  @Mock
  private NotificationEntityMapper notificationEntityMapper;

  @Mock
  private NotificationResponseMapper notificationResponseMapper;

  @InjectMocks
  private NotificationServiceImpl notificationService;

  @Test
  void save_success_withValidRequest() {
    // Arrange
    NotificationRequest request = NotificationRequest.builder()
        .title("Test Notification")
        .content("Test Content")
        .type(NotificationTypeEnum.INFO)
        .userName("testuser")
        .sourceId("source123")
        .sourceType(NotificationSourceTypeEnum.TASKS)
        .build();

    NotificationEntity mappedEntity = new NotificationEntity();
    mappedEntity.setTitle("Test Notification");
    mappedEntity.setContent("Test Content");
    mappedEntity.setType(NotificationTypeEnum.INFO);
    mappedEntity.setUserName("testuser");
    mappedEntity.setSourceId("source123");
    mappedEntity.setSourceType(NotificationSourceTypeEnum.TASKS);
    mappedEntity.setIsRead(false);

    NotificationEntity savedEntity = new NotificationEntity();
    savedEntity.setId("generated-id-123");
    savedEntity.setTitle("Test Notification");
    savedEntity.setContent("Test Content");
    savedEntity.setType(NotificationTypeEnum.INFO);
    savedEntity.setUserName("testuser");
    savedEntity.setSourceId("source123");
    savedEntity.setSourceType(NotificationSourceTypeEnum.TASKS);
    savedEntity.setIsRead(false);

    NotificationResponse expectedResponse = NotificationResponse.builder()
        .id("generated-id-123")
        .title("Test Notification")
        .content("Test Content")
        .type(NotificationTypeEnum.INFO)
        .userName("testuser")
        .sourceId("source123")
        .sourceType(NotificationSourceTypeEnum.TASKS)
        .isRead(false)
        .createdDate("2024-01-01T10:00:00")
        .build();

    // Mock static method
    try (MockedStatic<GeneratorUtil> mockedGeneratorUtil = Mockito.mockStatic(GeneratorUtil.class)) {
      mockedGeneratorUtil.when(GeneratorUtil::generateId).thenReturn("generated-id-123");

      when(notificationEntityMapper.map(request)).thenReturn(mappedEntity);
      when(notificationRepository.save(any(NotificationEntity.class))).thenReturn(savedEntity);
      when(notificationResponseMapper.map(savedEntity)).thenReturn(expectedResponse);
      doNothing().when(notificationRedisPublisher).sendMessage(expectedResponse);

      // Act
      NotificationResponse result = notificationService.save(request);

      // Assert
      assertNotNull(result);
      assertEquals(expectedResponse.getId(), result.getId());
      assertEquals(expectedResponse.getTitle(), result.getTitle());
      assertEquals(expectedResponse.getContent(), result.getContent());
      assertEquals(expectedResponse.getType(), result.getType());
      assertEquals(expectedResponse.getUserName(), result.getUserName());
      assertEquals(expectedResponse.getSourceId(), result.getSourceId());
      assertEquals(expectedResponse.getSourceType(), result.getSourceType());
      assertEquals(expectedResponse.getIsRead(), result.getIsRead());

      verify(notificationEntityMapper).map(request);
      verify(notificationRepository).save(any(NotificationEntity.class));
      verify(notificationResponseMapper).map(savedEntity);
      verify(notificationRedisPublisher).sendMessage(expectedResponse);
    }
  }

  @Test
  void getUnreadCount_success_withValidUserName() {
    // Arrange
    String userName = "testuser";
    Long expectedCount = 3L;

    when(notificationRepository.countByUserNameAndIsReadFalse(userName)).thenReturn(expectedCount);

    // Act
    Long result = notificationService.getUnreadCount(userName);

    // Assert
    assertNotNull(result);
    assertEquals(expectedCount, result);

    verify(notificationRepository).countByUserNameAndIsReadFalse(userName);
  }

  @Test
  void getUnreadCount_success_withZeroCount() {
    // Arrange
    String userName = "userWithNoUnread";
    Long expectedCount = 0L;

    when(notificationRepository.countByUserNameAndIsReadFalse(userName)).thenReturn(expectedCount);

    // Act
    Long result = notificationService.getUnreadCount(userName);

    // Assert
    assertNotNull(result);
    assertEquals(expectedCount, result);

    verify(notificationRepository).countByUserNameAndIsReadFalse(userName);
  }

  @Test
  void markAsRead_success_withValidId() throws BusinessException {
    // Arrange
    String notificationId = "notification123";
    NotificationEntity existingEntity = new NotificationEntity();
    existingEntity.setId(notificationId);
    existingEntity.setTitle("Test Notification");
    existingEntity.setIsRead(false);

    NotificationEntity updatedEntity = new NotificationEntity();
    updatedEntity.setId(notificationId);
    updatedEntity.setTitle("Test Notification");
    updatedEntity.setIsRead(true);

    when(notificationRepository.findById(notificationId)).thenReturn(Optional.of(existingEntity));
    when(notificationRepository.save(any(NotificationEntity.class))).thenReturn(updatedEntity);

    // Act
    notificationService.markAsRead(notificationId);

    // Assert
    verify(notificationRepository).findById(notificationId);
    verify(notificationRepository).save(any(NotificationEntity.class));
    // Verify that the entity's isRead was set to true
    assertEquals(true, existingEntity.getIsRead());
  }

  @Test
  void markAsRead_failed_whenNotificationNotFound() {
    // Arrange
    String notificationId = "nonexistent123";

    when(notificationRepository.findById(notificationId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      notificationService.markAsRead(notificationId);
    });

    assertEquals(ErrorCode.NOTIFICATION_NOT_FOUND.getCode(), exception.getCode());

    verify(notificationRepository).findById(notificationId);
    // Verify that save was never called since the notification was not found
    verify(notificationRepository, Mockito.never()).save(any(NotificationEntity.class));
  }

  @Test
  void findAllWithPaging_success_withSearchRequest() {
    // Arrange
    NotificationSearchRequest searchRequest = NotificationSearchRequest.builder()
        .pageSize(10)
        .search("test")
        .sourceTypes(Arrays.asList(NotificationSourceTypeEnum.TASKS))
        .isRead(false)
        .lastId("last123")
        .lastCreatedDate("2024-01-01T09:00:00")
        .build();

    String currentUserName = "currentuser";
    
    List<NotificationResponse> notifications = Arrays.asList(
        NotificationResponse.builder()
            .id("notification1")
            .title("Notification 1")
            .content("Content 1")
            .type(NotificationTypeEnum.INFO)
            .userName(currentUserName)
            .sourceId("source1")
            .sourceType(NotificationSourceTypeEnum.TASKS)
            .isRead(false)
            .createdDate("2024-01-01T10:00:00")
            .build(),
        NotificationResponse.builder()
            .id("notification2")
            .title("Notification 2")
            .content("Content 2")
            .type(NotificationTypeEnum.WARNING)
            .userName(currentUserName)
            .sourceId("source2")
            .sourceType(NotificationSourceTypeEnum.TASKS)
            .isRead(false)
            .createdDate("2024-01-01T11:00:00")
            .build()
    );

    NotificationCursor nextCursor = NotificationCursor.builder()
        .lastId("notification2")
        .lastCreatedDate("2024-01-01T11:00:00")
        .build();

    CursorPageResponse<NotificationResponse, NotificationCursor> expectedResponse =
        CursorPageResponse.<NotificationResponse, NotificationCursor>builder()
            .data(notifications)
            .nextCursor(nextCursor)
            .build();

    // Create a spy to mock the getUserName() method
    NotificationServiceImpl spyService = Mockito.spy(notificationService);
    Mockito.doReturn(currentUserName).when(spyService).getUserName();

    when(notificationRepository.findAllWithPaging(searchRequest, currentUserName)).thenReturn(expectedResponse);

    // Act
    CursorPageResponse<NotificationResponse, NotificationCursor> result = spyService.findAllWithPaging(searchRequest);

    // Assert
    assertNotNull(result);
    assertSame(expectedResponse, result);
    assertEquals(2, result.getData().size());
    assertEquals("notification1", result.getData().get(0).getId());
    assertEquals("notification2", result.getData().get(1).getId());
    assertNotNull(result.getNextCursor());
    assertEquals("notification2", result.getNextCursor().getLastId());

    verify(notificationRepository).findAllWithPaging(searchRequest, currentUserName);
  }

  @Test
  void findAllWithPaging_success_withEmptyResults() {
    // Arrange
    NotificationSearchRequest searchRequest = NotificationSearchRequest.builder()
        .pageSize(10)
        .search("nonexistent")
        .isRead(true)
        .build();

    String currentUserName = "userWithNoNotifications";

    CursorPageResponse<NotificationResponse, NotificationCursor> expectedResponse =
        CursorPageResponse.<NotificationResponse, NotificationCursor>builder()
            .data(Collections.emptyList())
            .nextCursor(null)
            .build();

    // Create a spy to mock the getUserName() method
    NotificationServiceImpl spyService = Mockito.spy(notificationService);
    Mockito.doReturn(currentUserName).when(spyService).getUserName();

    when(notificationRepository.findAllWithPaging(searchRequest, currentUserName)).thenReturn(expectedResponse);

    // Act
    CursorPageResponse<NotificationResponse, NotificationCursor> result = spyService.findAllWithPaging(searchRequest);

    // Assert
    assertNotNull(result);
    assertSame(expectedResponse, result);
    assertEquals(0, result.getData().size());
    assertEquals(null, result.getNextCursor());

    verify(notificationRepository).findAllWithPaging(searchRequest, currentUserName);
  }

  @Test
  void save_success_withMinimalRequest() {
    // Arrange
    NotificationRequest request = NotificationRequest.builder()
        .title("Minimal Notification")
        .userName("user123")
        .build();

    NotificationEntity mappedEntity = new NotificationEntity();
    mappedEntity.setTitle("Minimal Notification");
    mappedEntity.setUserName("user123");
    mappedEntity.setIsRead(false);

    NotificationEntity savedEntity = new NotificationEntity();
    savedEntity.setId("minimal-id-456");
    savedEntity.setTitle("Minimal Notification");
    savedEntity.setUserName("user123");
    savedEntity.setIsRead(false);

    NotificationResponse expectedResponse = NotificationResponse.builder()
        .id("minimal-id-456")
        .title("Minimal Notification")
        .userName("user123")
        .isRead(false)
        .createdDate("2024-01-01T12:00:00")
        .build();

    // Mock static method
    try (MockedStatic<GeneratorUtil> mockedGeneratorUtil = Mockito.mockStatic(GeneratorUtil.class)) {
      mockedGeneratorUtil.when(GeneratorUtil::generateId).thenReturn("minimal-id-456");

      when(notificationEntityMapper.map(request)).thenReturn(mappedEntity);
      when(notificationRepository.save(any(NotificationEntity.class))).thenReturn(savedEntity);
      when(notificationResponseMapper.map(savedEntity)).thenReturn(expectedResponse);
      doNothing().when(notificationRedisPublisher).sendMessage(expectedResponse);

      // Act
      NotificationResponse result = notificationService.save(request);

      // Assert
      assertNotNull(result);
      assertEquals(expectedResponse.getId(), result.getId());
      assertEquals(expectedResponse.getTitle(), result.getTitle());
      assertEquals(expectedResponse.getUserName(), result.getUserName());
      assertEquals(expectedResponse.getIsRead(), result.getIsRead());

      verify(notificationEntityMapper).map(request);
      verify(notificationRepository).save(any(NotificationEntity.class));
      verify(notificationResponseMapper).map(savedEntity);
      verify(notificationRedisPublisher).sendMessage(expectedResponse);
    }
  }

  @Test
  void getUnreadCount_success_withNullUserName() {
    // Arrange
    String userName = null;
    Long expectedCount = 0L;

    when(notificationRepository.countByUserNameAndIsReadFalse(userName)).thenReturn(expectedCount);

    // Act
    Long result = notificationService.getUnreadCount(userName);

    // Assert
    assertNotNull(result);
    assertEquals(expectedCount, result);

    verify(notificationRepository).countByUserNameAndIsReadFalse(userName);
  }

  @Test
  void getUnreadCount_success_withEmptyUserName() {
    // Arrange
    String userName = "";
    Long expectedCount = 0L;

    when(notificationRepository.countByUserNameAndIsReadFalse(userName)).thenReturn(expectedCount);

    // Act
    Long result = notificationService.getUnreadCount(userName);

    // Assert
    assertNotNull(result);
    assertEquals(expectedCount, result);

    verify(notificationRepository).countByUserNameAndIsReadFalse(userName);
  }

  @Test
  void markAsRead_success_withAlreadyReadNotification() throws BusinessException {
    // Arrange
    String notificationId = "alreadyRead123";
    NotificationEntity existingEntity = new NotificationEntity();
    existingEntity.setId(notificationId);
    existingEntity.setTitle("Already Read Notification");
    existingEntity.setIsRead(true); // Already read

    NotificationEntity updatedEntity = new NotificationEntity();
    updatedEntity.setId(notificationId);
    updatedEntity.setTitle("Already Read Notification");
    updatedEntity.setIsRead(true);

    when(notificationRepository.findById(notificationId)).thenReturn(Optional.of(existingEntity));
    when(notificationRepository.save(any(NotificationEntity.class))).thenReturn(updatedEntity);

    // Act
    notificationService.markAsRead(notificationId);

    // Assert
    verify(notificationRepository).findById(notificationId);
    verify(notificationRepository).save(any(NotificationEntity.class));
    // Verify that the entity's isRead remains true
    assertEquals(true, existingEntity.getIsRead());
  }

  @Test
  void findAllWithPaging_success_withDefaultSearchRequest() {
    // Arrange
    NotificationSearchRequest searchRequest = new NotificationSearchRequest();
    String currentUserName = "defaultuser";

    List<NotificationResponse> notifications = Arrays.asList(
        NotificationResponse.builder()
            .id("notification3")
            .title("Default Search Result")
            .content("Default Content")
            .type(NotificationTypeEnum.CRITICAL)
            .userName(currentUserName)
            .isRead(true)
            .createdDate("2024-01-01T13:00:00")
            .build()
    );

    CursorPageResponse<NotificationResponse, NotificationCursor> expectedResponse =
        CursorPageResponse.<NotificationResponse, NotificationCursor>builder()
            .data(notifications)
            .nextCursor(null)
            .build();

    // Create a spy to mock the getUserName() method
    NotificationServiceImpl spyService = Mockito.spy(notificationService);
    Mockito.doReturn(currentUserName).when(spyService).getUserName();

    when(notificationRepository.findAllWithPaging(searchRequest, currentUserName)).thenReturn(expectedResponse);

    // Act
    CursorPageResponse<NotificationResponse, NotificationCursor> result = spyService.findAllWithPaging(searchRequest);

    // Assert
    assertNotNull(result);
    assertEquals(1, result.getData().size());
    assertEquals("notification3", result.getData().get(0).getId());
    assertEquals("Default Search Result", result.getData().get(0).getTitle());
    assertEquals(null, result.getNextCursor());

    verify(notificationRepository).findAllWithPaging(searchRequest, currentUserName);
  }

  @Test
  void markAsRead_failed_withNullId() {
    // Arrange
    String notificationId = null;

    when(notificationRepository.findById(notificationId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      notificationService.markAsRead(notificationId);
    });

    assertEquals(ErrorCode.NOTIFICATION_NOT_FOUND.getCode(), exception.getCode());

    verify(notificationRepository).findById(notificationId);
    verify(notificationRepository, Mockito.never()).save(any(NotificationEntity.class));
  }

  @Test
  void markAsRead_failed_withEmptyId() {
    // Arrange
    String notificationId = "";

    when(notificationRepository.findById(notificationId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      notificationService.markAsRead(notificationId);
    });

    assertEquals(ErrorCode.NOTIFICATION_NOT_FOUND.getCode(), exception.getCode());

    verify(notificationRepository).findById(notificationId);
    verify(notificationRepository, Mockito.never()).save(any(NotificationEntity.class));
  }

  @Test
  void save_success_verifyIdGeneration() {
    // Arrange
    NotificationRequest request = NotificationRequest.builder()
        .title("ID Generation Test")
        .userName("testuser")
        .build();

    NotificationEntity mappedEntity = new NotificationEntity();
    mappedEntity.setTitle("ID Generation Test");
    mappedEntity.setUserName("testuser");
    mappedEntity.setIsRead(false);

    NotificationEntity savedEntity = new NotificationEntity();
    savedEntity.setId("ulid-generated-id");
    savedEntity.setTitle("ID Generation Test");
    savedEntity.setUserName("testuser");
    savedEntity.setIsRead(false);

    NotificationResponse expectedResponse = NotificationResponse.builder()
        .id("ulid-generated-id")
        .title("ID Generation Test")
        .userName("testuser")
        .isRead(false)
        .build();

    // Mock static method
    try (MockedStatic<GeneratorUtil> mockedGeneratorUtil = Mockito.mockStatic(GeneratorUtil.class)) {
      mockedGeneratorUtil.when(GeneratorUtil::generateId).thenReturn("ulid-generated-id");

      when(notificationEntityMapper.map(request)).thenReturn(mappedEntity);
      when(notificationRepository.save(any(NotificationEntity.class))).thenReturn(savedEntity);
      when(notificationResponseMapper.map(savedEntity)).thenReturn(expectedResponse);
      doNothing().when(notificationRedisPublisher).sendMessage(expectedResponse);

      // Act
      NotificationResponse result = notificationService.save(request);

      // Assert
      assertNotNull(result);
      assertEquals("ulid-generated-id", result.getId());

      // Verify that the ID was set on the entity before saving
      verify(notificationRepository).save(any(NotificationEntity.class));
      verify(notificationResponseMapper).map(savedEntity);
      verify(notificationRedisPublisher).sendMessage(expectedResponse);

      // Verify GeneratorUtil was called
      mockedGeneratorUtil.verify(GeneratorUtil::generateId);
    }
  }

  @Test
  void getUnreadCount_success_withLargeCount() {
    // Arrange
    String userName = "userWithManyNotifications";
    Long expectedCount = 999L;

    when(notificationRepository.countByUserNameAndIsReadFalse(userName)).thenReturn(expectedCount);

    // Act
    Long result = notificationService.getUnreadCount(userName);

    // Assert
    assertNotNull(result);
    assertEquals(expectedCount, result);

    verify(notificationRepository).countByUserNameAndIsReadFalse(userName);
  }
}
