package vn.com.mbbank.kanban.mbmonitor.common.dtos.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaJobTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/29/2024
 */
@Data
@Accessors(fluent = true, chain = true)
public class KafkaJobModel<T> {
  @JsonProperty("type")
  private KafkaJobTypeEnum type;
  
  @JsonProperty("configId")
  private T configId;

  @JsonProperty("cronTime")
  private String cronTime;
}
