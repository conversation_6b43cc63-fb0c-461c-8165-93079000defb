package vn.com.mbbank.kanban.mbmonitor.external.notification.repositories;

import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEntity;

/**
 * Repository table NotificationRepository.
 */
@Repository
public interface NotificationRepository
    extends JpaCommonRepository<NotificationEntity, String>, NotificationRepositoryCustom {

  /**
   * Counts the number of unread notifications for a specific user.
   *
   * @param userName the username for which to count unread notifications
   * @return the count of unread notifications associated with the given username
   */
  Long countByUserNameAndIsReadFalse(String userName);
}
