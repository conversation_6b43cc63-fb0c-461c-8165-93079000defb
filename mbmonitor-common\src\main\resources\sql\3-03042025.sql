CREATE TABLE MBMONITOR.SYS_LOG
(
    ID            VARCHAR2(50)  NOT NULL,
    FUNCTION      VARCHAR2(100) NOT NULL,
    ACTION        VARCHAR2(100) NOT NULL,
    LOG_BY        VARCHAR2(255) NOT NULL,
    LOG_DATE      TIMESTAMP(6)  NOT NULL,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255),
    MESSAGE       CLOB
) PARTITION BY RANGE (LOG_DATE) INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION PARTBASE VALUES LESS THAN (TO_DATE('2024-10-01 00:00:00', 'SYYYY-MM-DD HH24:MI:SS',
                                                 'NLS_CALENDAR=GREGORIAN'))
)
/

ALTER TABLE MBMONITOR.SYS_LOG
    ADD CONSTRAINT TASK_PK PRIMARY KEY (ID, LOG_DATE) USING INDEX LOCAL
/

CREATE INDEX MBMONITOR.SYS_LOG_ACTION_INDEX
    ON MBMONITOR.SYS_LOG (ACTION) LOCAL TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.SYS_LOG_FUNCTION_INDEX
    ON MBMONITOR.SYS_LOG (FUNCTION) LOCAL TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.SYS_LOG_LOG_BY_INDEX
    ON MBMONITOR.SYS_LOG (LOG_BY) LOCAL TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.SYS_LOG_LOG_DATE_ID_INDEX
    ON MBMONITOR.SYS_LOG (LOG_DATE, ID) LOCAL TABLESPACE INDEXS
/
CREATE SEQUENCE MBMONITOR.EXPORT_DATA_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999 NOCYCLE
    CACHE 50
/
CREATE TABLE MBMONITOR.EXPORT_DATA
(
    ID              VARCHAR2(50) NOT NULL
        CONSTRAINT EXPORT_DATA_pk
            PRIMARY KEY,
    FILE_NAME       VARCHAR2(100 CHAR),
    STATUS          VARCHAR2(30),
    EXTENSION       VARCHAR2(30),
    EXPORTED_BY     VARCHAR2(255),
    FILE_STORAGE_ID NUMBER,
    CREATED_DATE    TIMESTAMP(6),
    CREATED_BY      VARCHAR2(255),
    MODIFIED_DATE   TIMESTAMP(6),
    MODIFIED_BY     VARCHAR2(255)
)
/

CREATE INDEX MBMONITOR.EXPORT_DATA_FILE_STORAGE_ID_INDEX
    ON MBMONITOR.EXPORT_DATA (FILE_STORAGE_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.DATABASE_THRESHOLD_CONFIG
(
    ID                     VARCHAR2(50) PRIMARY KEY,
    NAME                   VARCHAR2(100 CHAR),
    DESCRIPTION            VARCHAR2(300 CHAR),
    DATABASE_CONNECTION_ID NUMBER              NOT NULL,
    SQL_COMMAND            CLOB,
    CRON_TIME              VARCHAR2(30),
    CONDITION_OPERATOR     VARCHAR2(30),
    CONDITION_VALUE        NUMBER,
    SERVICE_ID             VARCHAR2(50),
    APPLICATION_ID         VARCHAR2(50),
    PRIORITY_ID            NUMBER,
    RECIPIENT              VARCHAR2(255 CHAR),
    CONTENT                VARCHAR2(2000 CHAR),
    CONTENT_JSON           VARCHAR2(4000 CHAR),
    ACTIVE                 NUMBER(1) DEFAULT 1 NOT NULL
        CHECK (ACTIVE IN (0, 1)),
    CREATED_DATE           TIMESTAMP(6),
    CREATED_BY             VARCHAR2(255),
    MODIFIED_DATE          TIMESTAMP(6),
    MODIFIED_BY            VARCHAR2(255)
)
/
CREATE INDEX MBMONITOR.DATABASE_THRESHOLD_CONFIG_SERVICE_ID_INDEX
    ON MBMONITOR.DATABASE_THRESHOLD_CONFIG (SERVICE_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.DATABASE_THRESHOLD_CONFIG_APPLICATION_ID_INDEX
    ON MBMONITOR.DATABASE_THRESHOLD_CONFIG (APPLICATION_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.DATABASE_THRESHOLD_CONFIG_PRIORITY_ID_INDEX
    ON MBMONITOR.DATABASE_THRESHOLD_CONFIG (PRIORITY_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.DATABASE_THRESHOLD_CONFIG_EMAIL_CONFIG_ID_INDEX
    ON MBMONITOR.DATABASE_THRESHOLD_CONFIG (DATABASE_CONNECTION_ID) TABLESPACE INDEXS
/
-- teams config
CREATE TABLE MBMONITOR.TEAMS_CONFIG
(
    ID               VARCHAR2(50) NOT NULL ENABLE,
    CLIENT_ID        VARCHAR2(255),
    TENANT_ID        VARCHAR2(255),
    CLIENT_SECRET    VARCHAR2(500),
    EMAIL            VARCHAR2(100),
    PASSWORD         VARCHAR2(500),
    TYPE             VARCHAR2(50),
    MESSAGE_TEMPLATE CLOB,
    INTERVAL         VARCHAR2(50),
    INTERVAL_TYPE    VARCHAR2(50),
    DESCRIPTION      VARCHAR2(1000),
    CREATED_DATE     TIMESTAMP(6),
    CREATED_BY       VARCHAR2(255),
    MODIFIED_DATE    TIMESTAMP(6),
    MODIFIED_BY      VARCHAR2(255),
    PRIMARY KEY (ID)
)
/
CREATE INDEX MBMONITOR.CLIENT_ID_INDEX
    ON MBMONITOR.TEAMS_CONFIG (CLIENT_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.TENANT_ID_INDEX
    ON MBMONITOR.TEAMS_CONFIG (TENANT_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.EMAIL_INDEX
    ON MBMONITOR.TEAMS_CONFIG (EMAIL) TABLESPACE INDEXS
/
CREATE TABLE MBMONITOR.TEAMS_GROUP_CONFIG
(
    ID               VARCHAR2(50) NOT NULL ENABLE,
    TEAMS_CONFIG_ID  VARCHAR2(50),
    TEAMS_GROUP_ID   VARCHAR2(100),
    TEAMS_GROUP_NAME VARCHAR2(255),
    "POSITION"       NUMBER,
    CREATED_DATE     TIMESTAMP(6),
    CREATED_BY       VARCHAR2(255),
    MODIFIED_DATE    TIMESTAMP(6),
    MODIFIED_BY      VARCHAR2(255),
    PRIMARY KEY (ID)
)
/
CREATE INDEX MBMONITOR.TEAMS_GROUP_ID_INDEX
    ON MBMONITOR.TEAMS_GROUP_CONFIG (TEAMS_GROUP_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.TEAMS_CONFIG_ID_INDEX
    ON MBMONITOR.TEAMS_GROUP_CONFIG (TEAMS_CONFIG_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.TEAMS_GROUP_ID_INDEX
    ON MBMONITOR.TEAMS_GROUP_CONFIG (TEAMS_GROUP_ID) TABLESPACE INDEXS
/
CREATE TABLE MBMONITOR.TEAMS_USER_CONFIG
(
    ID                  VARCHAR2(50) NOT NULL ENABLE,
    TEAMS_GROUP_ID      VARCHAR2(50),
    TEAMS_USER_ID       VARCHAR2(100),
    EMAIL               VARCHAR2(100),
    CREATED_DATE        TIMESTAMP(6),
    CREATED_BY          VARCHAR2(255),
    MODIFIED_DATE       TIMESTAMP(6),
    MODIFIED_BY         VARCHAR2(255),
    TEAMS_GROUP_CHAT_ID VARCHAR2(50),
    PRIMARY KEY (ID)
)
/
CREATE INDEX MBMONITOR.TEAMS_GROUP_ID_INDEX
    ON MBMONITOR.TEAMS_USER_CONFIG (TEAMS_GROUP_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.TEAMS_USER_ID_INDEX
    ON MBMONITOR.TEAMS_USER_CONFIG (TEAMS_USER_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.EMAIL_INDEX
    ON MBMONITOR.TEAMS_USER_CONFIG (EMAIL) TABLESPACE INDEXS

-- update collect email config
ALTER TABLE MBMONITOR.COLLECT_EMAIL_CONFIG
    ADD (
        TYPE VARCHAR2(50),
        LAST_RECEIVED_EMAIL_DATE TIMESTAMP(6),
        ABSENCE_INTERVAL NUMBER,
        ALERT_REPEAT_INTERVAL NUMBER
        )
/
UPDATE MBMONITOR.COLLECT_EMAIL_CONFIG
SET TYPE = 'EVENT_BASE_ALERT';


CREATE TABLE MBMONITOR.EXECUTION
(
    ID                     VARCHAR2(50)       NOT NULL ENABLE PRIMARY KEY,
    NAME                   VARCHAR2(100 CHAR) NOT NULL,
    DESCRIPTION            VARCHAR2(300 CHAR),
    TYPE                   VARCHAR2(50)       NOT NULL,
    DATABASE_CONNECTION_ID NUMBER,
    EXECUTION_GROUP_ID     VARCHAR2(50)       NOT NULL,
    SCRIPT                 CLOB               NOT NULL,
    CREATED_DATE           TIMESTAMP(6),
    CREATED_BY             VARCHAR2(255),
    MODIFIED_DATE          TIMESTAMP(6),
    MODIFIED_BY            VARCHAR2(255)
)
/

CREATE INDEX MBMONITOR.EXECUTION_DATABASE_CONNECTION_ID_INDEX
    ON MBMONITOR.EXECUTION (DATABASE_CONNECTION_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.EXECUTION_EXECUTION_GROUP_ID_INDEX
    ON MBMONITOR.EXECUTION (EXECUTION_GROUP_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.EXECUTION_GROUP
(
    ID            VARCHAR2(50)       NOT NULL ENABLE PRIMARY KEY,
    NAME          VARCHAR2(100 CHAR) NOT NULL,
    DESCRIPTION   VARCHAR2(300 CHAR),
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255)
)
/

CREATE TABLE MBMONITOR.EXECUTION_HISTORY
(
    ID                    VARCHAR2(50)       NOT NULL,
    EXECUTION_NAME        VARCHAR2(100 CHAR) NOT NULL,
    EXECUTION_DESCRIPTION VARCHAR2(300 CHAR),
    EXECUTION_TYPE        VARCHAR2(50)       NOT NULL,
    EXECUTION_SCRIPT      CLOB               NOT NULL,
    EXECUTION_PARAMS      CLOB,
    STATUS                VARCHAR2(50)       NOT NULL,
    RESULT                CLOB,
    ERROR                 CLOB,
    EXECUTION_BY          VARCHAR2(255)      NOT NULL,
    START_TIME            TIMESTAMP(6)       NOT NULL,
    END_TIME              TIMESTAMP(6),
    CREATED_DATE          TIMESTAMP(6),
    CREATED_BY            VARCHAR2(255),
    MODIFIED_DATE         TIMESTAMP(6),
    MODIFIED_BY           VARCHAR2(255)
) PARTITION BY RANGE (START_TIME) INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION PARTBASE VALUES LESS THAN (TO_DATE('2024-10-01 00:00:00', 'SYYYY-MM-DD HH24:MI:SS',
                                                 'NLS_CALENDAR=GREGORIAN'))
)
/

ALTER TABLE MBMONITOR.EXECUTION_HISTORY
    ADD CONSTRAINT EXECUTION_HISTORY_PK PRIMARY KEY (ID, START_TIME) USING INDEX LOCAL
/

CREATE INDEX MBMONITOR.EXECUTION_HISTORY_EXECUTION_BY_INDEX
    ON MBMONITOR.EXECUTION_HISTORY (EXECUTION_BY) LOCAL TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.EXECUTION_PARAM
(
    ID            VARCHAR2(50)       NOT NULL ENABLE PRIMARY KEY,
    NAME          VARCHAR2(100 CHAR) NOT NULL,
    EXECUTION_ID  VARCHAR2(50)       NOT NULL,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255)
)
/

CREATE INDEX MBMONITOR.EXECUTION_PARAM_EXECUTION_ID_INDEX
    ON MBMONITOR.EXECUTION_PARAM (EXECUTION_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.VARIABLE
(
    ID            VARCHAR2(50)       NOT NULL ENABLE PRIMARY KEY,
    NAME          VARCHAR2(100 CHAR) NOT NULL,
    DESCRIPTION   VARCHAR2(300 CHAR),
    VALUE         VARCHAR2(2000)     NOT NULL,
    HIDDEN        NUMBER(1)          NOT NULL,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255)
)
/

-- Xóa table liên quan đến tính năng sql (SQL gộp vs execution)
DROP TABLE MBMONITOR.QUERY_SQL;
DROP TABLE MBMONITOR.GROUP_QUERY_SQL;
DROP TABLE MBMONITOR.PARAM_QUERY_SQL;

-- Detele role for SQL command feature
DELETE
FROM MBMONITOR.SYS_ROLE_PERMISSION sysRolePermission
WHERE sysRolePermission.MODULE_NAME = 'MULTI_SQL_QUERY';

-- Update MODULE_ID and MODULE_PARENT_ID to string
ALTER TABLE MBMONITOR.SYS_ROLE_PERMISSION
    MODIFY MODULE_ID VARCHAR2(50)
    MODIFY MODULE_PARENT_ID VARCHAR2(50)
/


/
CREATE TABLE MBMONITOR.SYS_USER_REFRESH_TOKEN
(
    "ID"            VARCHAR2(30)  NOT NULL ENABLE,
    "REFRESH_TOKEN" VARCHAR2(26)  NOT NULL,
    "EXPIRED_DATE"  TIMESTAMP(6)  NOT NULL,
    "USERNAME"      VARCHAR2(255) NOT NULL,
    "CREATED_BY"    VARCHAR2(100),
    "CREATED_DATE"  TIMESTAMP(6),
    "MODIFIED_BY"   VARCHAR2(100),
    "MODIFIED_DATE" TIMESTAMP(6),
    PRIMARY KEY (ID)
)
/
CREATE INDEX MBMONITOR.SYS_USER_REFRESH_TOKEN_REFRESH_TOKEN_INDEX
    ON MBMONITOR.SYS_USER_REFRESH_TOKEN (REFRESH_TOKEN) TABLESPACE INDEXS
/
ALTER TABLE MBMONITOR.DATABASE_CONNECTION
    ADD DATABASE_NAME VARCHAR2(128 CHAR)
    MODIFY USER_NAME VARCHAR2(128 CHAR)
    MODIFY PASSWORD VARCHAR2(128 CHAR)
/

CREATE TABLE MBMONITOR.NOTIFICATION
(
    ID            VARCHAR2(50),
    TITLE         VARCHAR2(100 CHAR),
    CONTENT       VARCHAR2(300 CHAR),
    TYPE          VARCHAR2(50), -- INFO, WARNING, CRITICAL
    USER_NAME     VARCHAR2(100 CHAR),
    SOURCE_ID     VARCHAR2(50),
    SOURCE_TYPE   VARCHAR2(50), -- TASKS, EVENTS
    IS_READ       NUMBER(1),    -- Use 'Y' or 'N' for boolean
    READ_DATE     TIMESTAMP(6),
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(100 CHAR),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(100 CHAR)
)
    PARTITION BY RANGE (CREATED_DATE) INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION PARTBASE VALUES LESS THAN (TO_DATE('2024-10-01 00:00:00', 'SYYYY-MM-DD HH24:MI:SS',
                                                 'NLS_CALENDAR=GREGORIAN'))
)
/

ALTER TABLE MBMONITOR.NOTIFICATION
    ADD CONSTRAINT NOTIFICATION_PK PRIMARY KEY (ID, CREATED_DATE) USING INDEX LOCAL
/

CREATE INDEX MBMONITOR.NOTIFICATION_USERNAME_INDEX
    ON MBMONITOR.NOTIFICATION (USER_NAME) LOCAL TABLESPACE INDEXS
/

CREATE INDEX NOTIFICATION_USER_NAME_CREATED_DATE_ID_INDEX
    ON MBMONITOR.NOTIFICATION (USER_NAME, CREATED_DATE DESC, ID) LOCAL TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.NOTIFICATION_SOURCE_TYPE_SOURCE_ID_INDEX
    ON MBMONITOR.NOTIFICATION (SOURCE_TYPE, SOURCE_ID) LOCAL TABLESPACE INDEXS
/