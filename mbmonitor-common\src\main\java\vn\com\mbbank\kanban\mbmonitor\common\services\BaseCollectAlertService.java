package vn.com.mbbank.kanban.mbmonitor.common.services;

import java.util.List;
import java.util.Map;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessRuntimeException;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.configs.ApplicationContextProvider;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.AlertBaseModelToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.impl.TelegramServiceImpl;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 1/3/2025
 */
public interface BaseCollectAlertService {

  /**
   * logic US Filter alert.
   *
   * @param alerts alerts
   * @return alerts after execute logic filterAlerts
   */
  List<AlertBaseModel> filterAlerts(List<AlertBaseModel> alerts);

  /**
   * logic US Filter alert.
   *
   * @param config config
   * @param alerts alerts
   * @return alerts after execute logic filterAlerts
   */
  default List<AlertBaseModel> filterAlerts(Map<String, Object> config,
                                            List<AlertBaseModel> alerts) {
    return filterAlerts(alerts);
  }

  /**
   * Logic filter collect alert.
   *
   * @param alerts alerts
   * @return alerts
   */
  List<AlertBaseModel> collectFilters(List<AlertBaseModel> alerts);

  /**
   * Logic filter collect alert.
   *
   * @param config config
   * @param alerts alerts
   * @return alerts
   */
  default List<AlertBaseModel> collectFilters(Map<String, Object> config,
                                              List<AlertBaseModel> alerts) {
    return collectFilters(alerts);
  }

  /**
   * Step logic US: modify alert before save into database.
   *
   * @param alerts alerts
   * @return alerts
   */
  List<AlertBaseModel> modifyAlerts(List<AlertBaseModel> alerts);

  /**
   * Step logic US: modify alert before save into database.
   *
   * @param config config
   * @param alerts alerts
   * @return alerts
   */
  default List<AlertBaseModel> modifyAlerts(Map<String, Object> config,
                                            List<AlertBaseModel> alerts) {
    return modifyAlerts(alerts);
  }

  /**
   * logic US maintenance.
   *
   * @param alerts alerts
   * @return alerts after execute logic maintenance
   */
  List<AlertBaseModel> maintenanceAlerts(List<AlertBaseModel> alerts);

  /**
   * logic US maintenance.
   *
   * @param config config
   * @param alerts alerts
   * @return alerts after execute logic maintenance
   */
  default List<AlertBaseModel> maintenanceAlerts(Map<String, Object> config,
                                                 List<AlertBaseModel> alerts) {
    return maintenanceAlerts(alerts);
  }

  /**
   * Save alert into database.
   *
   * @param alerts alerts
   * @return alerts
   */
  List<AlertBaseModel> saveAlerts(List<AlertBaseModel> alerts);

  /**
   * Save alert into database.
   *
   * @param config config
   * @param alerts alerts
   * @return alerts
   */
  default List<AlertBaseModel> saveAlerts(Map<String, Object> config, List<AlertBaseModel> alerts) {
    return saveAlerts(alerts);
  }


  /**
   * Logic event action affter save alert into database.
   *
   * @param config config
   * @param alerts alerts
   */
  default void alertAction(Map<String, Object> config, List<AlertBaseModel> alerts) {
    //TODO logic
  }

  /**
   * Logic collect end.
   *
   * @param config       config
   * @param beforeAlerts beforeAlerts
   * @param endAlerts    endAlerts
   * @return alerts
   */
  default List<AlertBaseModel> collectAlertsEnd(Map<String, Object> config,
                                                List<AlertBaseModel> beforeAlerts,
                                                List<AlertBaseModel> endAlerts) {
    //@Override  logic
    return endAlerts;
  }

  /**
   * Logic before collect data.
   *
   * @param config       config
   * @param beforeAlerts beforeAlerts
   * @return data affer logic  collectAlertsBefore
   */
  default List<AlertBaseModel> collectAlertsBefore(Map<String, Object> config,
                                                   List<AlertBaseModel> beforeAlerts) {
    return beforeAlerts;
  }

  /**
   * send alert to telegram.
   *
   * @param alerts alerts
   */
  default void sendTelegram(List<AlertEntity> alerts) {
    var telegramService = ApplicationContextProvider.getBean(TelegramServiceImpl.class);
    if (!KanbanCommonUtil.isEmpty(telegramService)) {
      telegramService.sendAlertKafka(alerts);
    }
  }

  /**
   * alert flow.
   *
   * @param config config
   * @param alerts alerts
   * @return alerts after save
   * @throws BusinessRuntimeException runtime exception
   */
  default List<AlertBaseModel> collectAlerts(Map<String, Object> config,
                                             List<AlertBaseModel> alerts)
      throws BusinessRuntimeException {
    List<AlertBaseModel> results = collectAlertsBefore(config, alerts);
    if (CollectionUtils.isEmpty(results)) {
      return collectAlertsEnd(config, alerts, results);
    }
    results = filterAlerts(config, results);
    results = collectFilters(config, results);
    results = modifyAlerts(config, results);
    results = maintenanceAlerts(config, results);
    results = saveAlerts(config, results);
    // trigger event action.
    alertAction(config, results);
    sendTelegram(AlertBaseModelToEntityMapper.INSTANCE.mapTo(results));
    results = collectAlertsEnd(config, alerts, results);
    return results;
  }

  /**
   * alert flow.
   *
   * @param alerts alerts
   * @return alerts after save
   * @throws BusinessRuntimeException runtime exception
   */
  default List<AlertBaseModel> collectAlerts(List<AlertBaseModel> alerts)
      throws BusinessRuntimeException {
    return collectAlerts(null, alerts);
  }
}
