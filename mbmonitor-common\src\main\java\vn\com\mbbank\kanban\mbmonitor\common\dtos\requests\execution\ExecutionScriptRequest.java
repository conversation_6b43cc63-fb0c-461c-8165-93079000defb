package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;

/**
 * ExecutionScriptRequest.
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExecutionScriptRequest {
  @Size(max = CommonConstants.COMMON_QUERY_SQL_NAME_MAX_LENGTH)
  @Size(min = 1)
  @NotBlank
  String name;
  @Size(max = CommonConstants.COMMON_DESCRIPTION_MAX_LENGTH)
  String description;
  @NotNull
  ExecutionTypeEnum type;
  @NotNull
  String executionBy;
  @NotNull
  String script;
  @Builder.Default
  List<ExecuteScriptParamModel> params = new ArrayList<>();
}
