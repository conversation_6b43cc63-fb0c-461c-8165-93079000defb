package vn.com.mbbank.kanban.mbmonitor.external.notification.repositories.impl;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request.NotificationCursor;
import vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request.NotificationSearchRequest;
import vn.com.mbbank.kanban.mbmonitor.external.notification.repositories.NotificationRepositoryCustom;

/**
 * NotificationRepositoryCustomImpl.
 */
@RequiredArgsConstructor
public class NotificationRepositoryCustomImpl implements NotificationRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public CursorPageResponse<NotificationResponse, NotificationCursor> findAllWithPaging(
      NotificationSearchRequest searchRequest, String userName) {

    var query = new PrepareQuery("""
        SELECT
          ID,
          TITLE,
          CONTENT,
          TYPE,
          USER_NAME,
          SOURCE_ID,
          SOURCE_TYPE,
          IS_READ,
          READ_DATE,
          CREATED_DATE
        FROM NOTIFICATION notification
        WHERE USER_NAME = :userName
        """, "userName", userName)
        .append(buildIsReadQuery(searchRequest.getIsRead()))
        .append(buildSearchLike(searchRequest.getSearch()))
        .append(buildSourceTypeIn(searchRequest.getSourceTypes()))
        .append(buildQueryCursor(searchRequest.getLastId(), searchRequest.getLastCreatedDate()))
        .append(" ORDER BY CREATED_DATE DESC, ID ASC")
        .append(" FETCH FIRST " + (searchRequest.getPageSize() + 1) + " ROWS ONLY ");

    // lấy dư 1 phần tử để xem có page tiếp theo không.
    var result = sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), NotificationResponse.class);
    if (result.size() > searchRequest.getPageSize()) {
      var lastItem = result.get(result.size() - 2);
      return new CursorPageResponse<>(result.subList(0, searchRequest.getPageSize()),
          new NotificationCursor(lastItem.getId(), lastItem.getCreatedDate())
      );
    }
    return new CursorPageResponse<>(result, null);
  }

  protected PrepareQuery buildIsReadQuery(Boolean isRead) {
    if (Objects.isNull(isRead)) {
      return null;
    }
    return new PrepareQuery(" AND notification.IS_READ = :isRead ", "isRead", isRead ? 1 : 0);
  }

  protected PrepareQuery buildSourceTypeIn(List<NotificationSourceTypeEnum> sourceTypes) {
    if (KanbanCommonUtil.isEmpty(sourceTypes)) {
      return null;
    }
    return new PrepareQuery(" AND notification.SOURCE_TYPE IN (:sourceTypes)", "sourceTypes",
        sourceTypes.stream().map(Enum::name).toList());
  }

  protected PrepareQuery buildSearchLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    return new PrepareQuery("AND (")
        .append(new PrepareQuery("LOWER(notification.TITLE) LIKE :search"),
            LikeMatcher.CONTAINING)
        .append(
            new PrepareQuery(" OR LOWER(notification.CONTENT) LIKE :search",
                Map.of("search", search.toLowerCase())),
            LikeMatcher.CONTAINING)
        .append(")");
  }

  protected PrepareQuery buildQueryCursor(String lastId, String lastCreatedDate) {
    if (StringUtils.isBlank(lastId) || StringUtils.isBlank(lastCreatedDate)) {
      return null;
    }
    return new PrepareQuery("""
         AND (
           (CREATED_DATE < :lastCreatedDate)
           OR (CREATED_DATE = :lastCreatedDate AND ID > :lastId)
         )
        """, Map.of("lastCreatedDate", Timestamp.valueOf(lastCreatedDate),
        "lastId", lastId));
  }

}