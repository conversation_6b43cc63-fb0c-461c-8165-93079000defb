package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.VariableResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;

/**
 * ExecutionResponse.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExecutionResponse {
  String id;
  String name;
  String description;
  ExecutionTypeEnum type;
  Long databaseConnectionId;
  String executionGroupId;
  String executionGroupName;
  String script;
  List<VariableResponse> variables;
}
