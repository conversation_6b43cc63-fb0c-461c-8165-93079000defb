package vn.com.mbbank.kanban.mbmonitor.external.notification.repositories;

import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationResponse;
import vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request.NotificationCursor;
import vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request.NotificationSearchRequest;

/**
 * NotificationRepositoryCustom.
 */
public interface NotificationRepositoryCustom {

  /**
   * find all Notification with paging.
   *
   * @param searchRequest searchRequest
   * @param userName userName
   * @return page of Notification
   */
  CursorPageResponse<NotificationResponse, NotificationCursor> findAllWithPaging(
      NotificationSearchRequest searchRequest, String userName);

}