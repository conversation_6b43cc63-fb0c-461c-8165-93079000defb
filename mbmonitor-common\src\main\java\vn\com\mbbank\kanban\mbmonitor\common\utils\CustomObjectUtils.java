package vn.com.mbbank.kanban.mbmonitor.common.utils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;

/**
 * CustomObjectUtils.
 */
public class CustomObjectUtils {

  private CustomObjectUtils() {

  }

  /**
   * Generates a map of key-value pairs representing alert conditions and their corresponding values.
   *
   * @param alert the {@link AlertEntity} object containing the alert details such as content, priority, and recipient.
   * @param customObjects a list of {@link CustomObjectEntity} objects to be evaluated with the alert content.
   *                      If the list is empty or null, no additional custom mappings are added.
   * @return a {@link Map} containing the alert's details and evaluated custom object values.
   *                      If the {@code alert} parameter is {@code null}, the method returns an empty map.
   */
  public static Map<String, Object> getAlertConditionValueMap(AlertEntity alert,
                                                              List<CustomObjectEntity> customObjects) {
    if (Objects.isNull(alert)) {
      return Collections.emptyMap();
    }
    var mapValue = new HashMap<String, Object>();
    mapValue.put("content", alert.getContent());
    mapValue.put("priority", alert.getAlertPriorityConfigId());
    mapValue.put("recipient", alert.getRecipient());
    if (!CollectionUtils.isEmpty(customObjects)) {
      customObjects.forEach(customObject -> mapValue.put(String.valueOf(customObject.getId()),
          CustomObjectUtils.evaluate(alert.getContent(), customObject)));
    }
    return mapValue;
  }

  /**
   * evaluate custom object.
   *
   * @param input        alert content
   * @param customObject custom object
   * @return evaluated value
   */
  public static String evaluate(String input, CustomObjectEntity customObject) {
    if (StringUtils.isBlank(input) || Objects.isNull(customObject)) {
      return "";
    }
    return switch (customObject.getType()) {
      case REGEX -> evaluateRegex(input, customObject.getRegex());
      case INDEX_TO_INDEX -> evaluateIndexToIndex(input, customObject.getFromIndex(), customObject.getToIndex());
      case KEYWORD_TO_KEYWORD ->
          evaluateKeywordToKeyword(input, customObject.getFromKeyword(), customObject.getToKeyword());
    };
  }

  private static String evaluateIndexToIndex(String alertContent, Integer fromIndex, Integer toIndex) {
    try {
      return alertContent.substring(fromIndex, toIndex + 1);
    } catch (IndexOutOfBoundsException exception) {
      return "";
    }
  }

  private static String evaluateRegex(String alertContent, String regex) {
    Pattern pattern = Pattern.compile(regex);
    Matcher matcher = pattern.matcher(alertContent);
    var matched = matcher.find();
    if (!matched || matcher.groupCount() == 0) {
      return "";
    }
    return matcher.group(1);
  }

  private static String evaluateKeywordToKeyword(String alertContent, String fromKeyword, String toKeyword) {
    var fromIndex = alertContent.indexOf(fromKeyword) + fromKeyword.length();
    var toIndex = alertContent.indexOf(toKeyword);
    if (fromIndex > toIndex) {
      return "";
    }
    return evaluateIndexToIndex(alertContent, fromIndex, toIndex - 1);
  }
}
