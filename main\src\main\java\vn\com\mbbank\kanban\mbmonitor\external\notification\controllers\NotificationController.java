package vn.com.mbbank.kanban.mbmonitor.external.notification.controllers;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationResponse;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ExternalNotificationUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request.NotificationCursor;
import vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request.NotificationSearchRequest;
import vn.com.mbbank.kanban.mbmonitor.external.notification.services.NotificationService;

/**
 * ExecutionController.
 */
@RestController
@RequestMapping(ExternalNotificationUrl.NOTIFICATION)
@RequiredArgsConstructor
public class NotificationController extends BaseController {

  private final NotificationService notificationService;

  /**
   * create notification.
   *
   * @param request request
   * @return NotificationResponse
   */
  @PostMapping
  public ResponseData<NotificationResponse> save(
      @RequestBody NotificationRequest request) {
    return ResponseUtils.success(notificationService.save(request));
  }

  /**
   * Retrieves the total number of unread messages for the currently authenticated user.
   *
   * @return a ResponseData object containing the total count of unread messages as a Long
   */
  @GetMapping("/unread-count")
  public ResponseData<Long> getUnreadCount() {
    return ResponseUtils.success(notificationService.getUnreadCount(getUserId()));
  }


  /**
   * Marks a specific notification as read by its ID.
   *
   * @param id the unique identifier of the notification to be marked as read
   * @return a ResponseData object containing the success message "OK"
   * @throws BusinessException if an error occurs during the operation
   */
  @PutMapping("/{id}/mark-as-read")
  public ResponseData<String> markAsRead(@PathVariable String id) throws BusinessException {
    notificationService.markAsRead(id);
    return ResponseUtils.success("OK");
  }


  /**
   * Retrieves a paginated list of notifications based on the search criteria specified
   * in the NotificationSearchRequest.
   *
   * @param searchRequest the search request containing filters, pagination, and cursor information
   * @return a ResponseData object containing a CursorPageResponse with a list of NotificationResponse objects
   *         and the corresponding NotificationCursor for the next page
   */
  @GetMapping
  public ResponseData<CursorPageResponse<NotificationResponse, NotificationCursor>> findAll(
      @ModelAttribute NotificationSearchRequest searchRequest) {
    return ResponseUtils.success(notificationService.findAllWithPaging(searchRequest));
  }
}
