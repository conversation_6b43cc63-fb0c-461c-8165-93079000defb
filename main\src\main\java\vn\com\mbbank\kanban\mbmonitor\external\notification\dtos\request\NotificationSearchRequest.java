package vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationSourceTypeEnum;

/**
 * Model view attribute to filter in alert table tab report.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@FieldDefaults(level = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = true)
public class NotificationSearchRequest extends NotificationCursor {
  int pageSize = 10;
  String search;
  List<NotificationSourceTypeEnum> sourceTypes;
  Boolean isRead;
}
