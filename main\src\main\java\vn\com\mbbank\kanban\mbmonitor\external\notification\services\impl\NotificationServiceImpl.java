package vn.com.mbbank.kanban.mbmonitor.external.notification.services.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.notification.NotificationEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.notification.NotificationResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;
import vn.com.mbbank.kanban.mbmonitor.external.notification.configs.redis.NotificationRedisPublisher;
import vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request.NotificationCursor;
import vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request.NotificationSearchRequest;
import vn.com.mbbank.kanban.mbmonitor.external.notification.repositories.NotificationRepository;
import vn.com.mbbank.kanban.mbmonitor.external.notification.services.NotificationService;

@Service
@RequiredArgsConstructor
public class NotificationServiceImpl extends BaseServiceImpl<NotificationEntity, String>
    implements NotificationService {
  private final NotificationRepository notificationRepository;
  private final NotificationRedisPublisher notificationRedisPublisher;
  private final NotificationEntityMapper notificationEntityMapper = NotificationEntityMapper.INSTANCE;
  private final NotificationResponseMapper notificationResponseMapper = NotificationResponseMapper.INSTANCE;

  @Override
  protected JpaCommonRepository<NotificationEntity, String> getRepository() {
    return notificationRepository;
  }

  @Override
  public NotificationResponse save(NotificationRequest notificationRequest) {
    var notification = notificationEntityMapper.map(notificationRequest);
    notification.setId(GeneratorUtil.generateId());
    var entity = notificationRepository.save(notification);
    var res = notificationResponseMapper.map(entity);
    notificationRedisPublisher.sendMessage(res);
    return res;
  }

  @Override
  public Long getUnreadCount(String userName) {
    return notificationRepository.countByUserNameAndIsReadFalse(userName);
  }

  @Override
  public void markAsRead(String id) throws BusinessException {
    var notification =
        notificationRepository.findById(id).orElseThrow(() -> new BusinessException(ErrorCode.NOTIFICATION_NOT_FOUND));
    notification.setIsRead(true);
    notificationRepository.save(notification);
  }

  @Override
  public CursorPageResponse<NotificationResponse, NotificationCursor> findAllWithPaging(
      NotificationSearchRequest searchRequest) {
    return notificationRepository.findAllWithPaging(searchRequest, getUserName());
  }
}
