package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;

/**
 * ExecutionRequest.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExecutionRequest {
  String id;
  @Size(max = CommonConstants.COMMON_QUERY_SQL_NAME_MAX_LENGTH)
  @Size(min = 1)
  @NotBlank
  String name;
  @Size(max = CommonConstants.COMMON_DESCRIPTION_MAX_LENGTH)
  String description;
  @NotNull
  ExecutionTypeEnum type;
  Long databaseConnectionId;
  @NotNull
  String executionGroupId;
  @Size(min = 1)
  @Size(max = CommonConstants.EXECUTION_SCRIPT_MAX_LENGTH)
  String script;
}
