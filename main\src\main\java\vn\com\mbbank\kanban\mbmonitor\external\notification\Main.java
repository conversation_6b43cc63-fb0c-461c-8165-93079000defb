package vn.com.mbbank.kanban.mbmonitor.external.notification;

import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * Main class for starting the Spring Boot application.
 */
@SpringBootApplication(scanBasePackages = {"vn.com.mbbank"})
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableEncryptableProperties
@OpenAPIDefinition(info = @Info(title = "mbmonitor-notification swagger API", version = "3.0",
    description = "mbmonitor-notification team swagger API"))
@EntityScan(basePackages = "vn.com.mbbank.kanban.mbmonitor.common.entities")
public class Main {

  /**
   * Entry point for the Spring Boot application.
   *
   * @param args command-line arguments passed when starting the application.
   */
  public static void main(String[] args) {
    SpringApplication.run(Main.class, args);
  }
}