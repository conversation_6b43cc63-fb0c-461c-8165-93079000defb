package vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request.NotificationCursor;
import vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request.NotificationSearchRequest;
import vn.com.mbbank.kanban.mbmonitor.external.notification.repositories.impl.NotificationRepositoryCustomImpl;

@ExtendWith(MockitoExtension.class)
public class NotificationRepositoryCustomImplTest {

  @Mock
  SqlQueryUtil sqlQueryUtil;

  @Mock
  SqlQueryUtil.SqlQueryModel sqlQueryModel;

  @InjectMocks
  @Spy
  NotificationRepositoryCustomImpl repositoryCustom;

  @Test
  void findAllWithPaging_success_withEmptyResults() {
    // Arrange
    NotificationSearchRequest request = new NotificationSearchRequest();
    request.setPageSize(10);
    String userName = "testuser";

    when(sqlQueryUtil.queryModel()).thenReturn(sqlQueryModel);
    when(sqlQueryModel.queryForList(anyString(), anyMap(), eq(NotificationResponse.class)))
        .thenReturn(Collections.emptyList());

    // Act
    CursorPageResponse<NotificationResponse, NotificationCursor> result = 
        repositoryCustom.findAllWithPaging(request, userName);

    // Assert
    assertNotNull(result);
    assertEquals(0, result.getData().size());
    assertNull(result.getNextCursor());

    verify(sqlQueryUtil).queryModel();
    verify(sqlQueryModel).queryForList(anyString(), anyMap(), eq(NotificationResponse.class));
  }

  @Test
  void findAllWithPaging_success_withResults() {
    // Arrange
    NotificationSearchRequest request = new NotificationSearchRequest();
    request.setPageSize(2);
    request.setSearch("test");
    request.setIsRead(false);
    request.setSourceTypes(Arrays.asList(NotificationSourceTypeEnum.EVENTS));
    String userName = "testuser";

    List<NotificationResponse> mockResults = Arrays.asList(
        NotificationResponse.builder()
            .id("notification1")
            .title("Test Notification 1")
            .content("Test Content 1")
            .type(NotificationTypeEnum.INFO)
            .userName(userName)
            .isRead(false)
            .createdDate("2024-01-01T10:00:00")
            .build(),
        NotificationResponse.builder()
            .id("notification2")
            .title("Test Notification 2")
            .content("Test Content 2")
            .type(NotificationTypeEnum.WARNING)
            .userName(userName)
            .isRead(false)
            .createdDate("2024-01-01T11:00:00")
            .build()
    );

    when(sqlQueryUtil.queryModel()).thenReturn(sqlQueryModel);
    when(sqlQueryModel.queryForList(anyString(), anyMap(), eq(NotificationResponse.class)))
        .thenReturn(mockResults);

    // Act
    CursorPageResponse<NotificationResponse, NotificationCursor> result = 
        repositoryCustom.findAllWithPaging(request, userName);

    // Assert
    assertNotNull(result);
    assertEquals(2, result.getData().size());
    assertEquals("notification1", result.getData().get(0).getId());
    assertEquals("notification2", result.getData().get(1).getId());
    assertNull(result.getNextCursor()); // No next cursor since results size equals pageSize

    verify(sqlQueryUtil).queryModel();
    verify(sqlQueryModel).queryForList(anyString(), anyMap(), eq(NotificationResponse.class));
  }

  @Test
  void findAllWithPaging_success_withNextCursor() {
    // Arrange
    NotificationSearchRequest request = new NotificationSearchRequest();
    request.setPageSize(2);
    String userName = "testuser";

    // Mock 3 results (pageSize + 1) to trigger next cursor creation
    List<NotificationResponse> mockResults = Arrays.asList(
        NotificationResponse.builder()
            .id("notification1")
            .title("Test Notification 1")
            .createdDate("2024-01-01T10:00:00")
            .build(),
        NotificationResponse.builder()
            .id("notification2")
            .title("Test Notification 2")
            .createdDate("2024-01-01T11:00:00")
            .build(),
        NotificationResponse.builder()
            .id("notification3")
            .title("Test Notification 3")
            .createdDate("2024-01-01T12:00:00")
            .build()
    );

    when(sqlQueryUtil.queryModel()).thenReturn(sqlQueryModel);
    when(sqlQueryModel.queryForList(anyString(), anyMap(), eq(NotificationResponse.class)))
        .thenReturn(mockResults);

    // Act
    CursorPageResponse<NotificationResponse, NotificationCursor> result = 
        repositoryCustom.findAllWithPaging(request, userName);

    // Assert
    assertNotNull(result);
    assertEquals(2, result.getData().size()); // Should return only pageSize items
    assertEquals("notification1", result.getData().get(0).getId());
    assertEquals("notification2", result.getData().get(1).getId());
    
    // Should have next cursor based on the second item (index pageSize - 1)
    assertNotNull(result.getNextCursor());
    assertEquals("notification2", result.getNextCursor().getLastId());
    assertEquals("2024-01-01T11:00:00", result.getNextCursor().getLastCreatedDate());

    verify(sqlQueryUtil).queryModel();
    verify(sqlQueryModel).queryForList(anyString(), anyMap(), eq(NotificationResponse.class));
  }

  @Test
  void findAllWithPaging_success_withCursor() {
    // Arrange
    NotificationSearchRequest request = new NotificationSearchRequest();
    request.setPageSize(10);
    request.setLastId("lastId123");
    request.setLastCreatedDate("2024-01-01 09:00:00");
    String userName = "testuser";

    when(sqlQueryUtil.queryModel()).thenReturn(sqlQueryModel);
    when(sqlQueryModel.queryForList(anyString(), anyMap(), eq(NotificationResponse.class)))
        .thenReturn(Collections.emptyList());

    // Act
    CursorPageResponse<NotificationResponse, NotificationCursor> result = 
        repositoryCustom.findAllWithPaging(request, userName);

    // Assert
    assertNotNull(result);
    assertEquals(0, result.getData().size());
    assertNull(result.getNextCursor());

    verify(sqlQueryUtil).queryModel();
    verify(sqlQueryModel).queryForList(anyString(), anyMap(), eq(NotificationResponse.class));
  }

  @Test
  void buildIsReadQuery_success_withTrue() {
    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildIsReadQuery", true);

    // Assert
    assertNotNull(result);
  }

  @Test
  void buildIsReadQuery_success_withFalse() {
    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildIsReadQuery", false);

    // Assert
    assertNotNull(result);
  }

  @Test
  void buildIsReadQuery_success_withNull() {
    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildIsReadQuery", (Boolean) null);

    // Assert
    assertNull(result);
  }

  @Test
  void buildSourceTypeIn_success_withSourceTypes() {
    // Arrange
    List<NotificationSourceTypeEnum> sourceTypes = Arrays.asList(
        NotificationSourceTypeEnum.EVENTS, 
        NotificationSourceTypeEnum.TASKS
    );

    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildSourceTypeIn", sourceTypes);

    // Assert
    assertNotNull(result);
  }

  @Test
  void buildSourceTypeIn_success_withEmptyList() {
    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildSourceTypeIn", Collections.emptyList());

    // Assert
    assertNull(result);
  }

  @Test
  void buildSourceTypeIn_success_withNull() {
    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildSourceTypeIn", (List<NotificationSourceTypeEnum>) null);

    // Assert
    assertNull(result);
  }

  @Test
  void buildSearchLike_success_withSearchTerm() {
    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildSearchLike", "test search");

    // Assert
    assertNotNull(result);
  }

  @Test
  void buildSearchLike_success_withBlankSearch() {
    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildSearchLike", "");

    // Assert
    assertNull(result);
  }

  @Test
  void buildSearchLike_success_withNullSearch() {
    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildSearchLike", (String) null);

    // Assert
    assertNull(result);
  }

  @Test
  void buildQueryCursor_success_withValidCursor() {
    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildQueryCursor", 
        "lastId123", "2024-01-01 10:00:00");

    // Assert
    assertNotNull(result);
  }

  @Test
  void buildQueryCursor_success_withBlankId() {
    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildQueryCursor", 
        "", "2024-01-01 10:00:00");

    // Assert
    assertNull(result);
  }

  @Test
  void buildQueryCursor_success_withBlankDate() {
    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildQueryCursor", 
        "lastId123", "");

    // Assert
    assertNull(result);
  }

  @Test
  void buildQueryCursor_success_withNullValues() {
    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildQueryCursor",
        null, null);

    // Assert
    assertNull(result);
  }

  @Test
  void findAllWithPaging_success_withAllFilters() {
    // Arrange
    NotificationSearchRequest request = new NotificationSearchRequest();
    request.setPageSize(5);
    request.setSearch("important");
    request.setIsRead(true);
    request.setSourceTypes(Arrays.asList(NotificationSourceTypeEnum.EVENTS, NotificationSourceTypeEnum.TASKS));
    request.setLastId("cursor123");
    request.setLastCreatedDate("2024-01-01 08:00:00");
    String userName = "adminuser";

    List<NotificationResponse> mockResults = Arrays.asList(
        NotificationResponse.builder()
            .id("notification1")
            .title("Important Notification")
            .content("Important Content")
            .type(NotificationTypeEnum.CRITICAL)
            .userName(userName)
            .isRead(true)
            .createdDate("2024-01-01 10:00:00")
            .build()
    );

    when(sqlQueryUtil.queryModel()).thenReturn(sqlQueryModel);
    when(sqlQueryModel.queryForList(anyString(), anyMap(), eq(NotificationResponse.class)))
        .thenReturn(mockResults);

    // Act
    CursorPageResponse<NotificationResponse, NotificationCursor> result =
        repositoryCustom.findAllWithPaging(request, userName);

    // Assert
    assertNotNull(result);
    assertEquals(1, result.getData().size());
    assertEquals("notification1", result.getData().get(0).getId());
    assertEquals("Important Notification", result.getData().get(0).getTitle());
    assertEquals(NotificationTypeEnum.CRITICAL, result.getData().get(0).getType());
    assertEquals(true, result.getData().get(0).getIsRead());
    assertNull(result.getNextCursor());

    verify(sqlQueryUtil).queryModel();
    verify(sqlQueryModel).queryForList(anyString(), anyMap(), eq(NotificationResponse.class));
  }

  @Test
  void findAllWithPaging_success_withMinimalRequest() {
    // Arrange
    NotificationSearchRequest request = new NotificationSearchRequest();
    // Using default pageSize = 10
    String userName = "simpleuser";

    when(sqlQueryUtil.queryModel()).thenReturn(sqlQueryModel);
    when(sqlQueryModel.queryForList(anyString(), anyMap(), eq(NotificationResponse.class)))
        .thenReturn(Collections.emptyList());

    // Act
    CursorPageResponse<NotificationResponse, NotificationCursor> result =
        repositoryCustom.findAllWithPaging(request, userName);

    // Assert
    assertNotNull(result);
    assertEquals(0, result.getData().size());
    assertNull(result.getNextCursor());

    verify(sqlQueryUtil).queryModel();
    verify(sqlQueryModel).queryForList(anyString(), anyMap(), eq(NotificationResponse.class));
  }

  @Test
  void buildSearchLike_success_withWhitespaceSearch() {
    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildSearchLike", "   ");

    // Assert
    assertNull(result);
  }

  @Test
  void buildSourceTypeIn_success_withSingleSourceType() {
    // Arrange
    List<NotificationSourceTypeEnum> sourceTypes = Arrays.asList(NotificationSourceTypeEnum.EVENTS);

    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildSourceTypeIn", sourceTypes);

    // Assert
    assertNotNull(result);
  }

  @Test
  void buildQueryCursor_success_withOnlyId() {
    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildQueryCursor",
        "onlyId123", null);

    // Assert
    assertNull(result);
  }

  @Test
  void buildQueryCursor_success_withOnlyDate() {
    // Act
    var result = ReflectionTestUtils.invokeMethod(repositoryCustom, "buildQueryCursor",
        null, "2024-01-01 10:00:00");

    // Assert
    assertNull(result);
  }
}
