package vn.com.mbbank.kanban.mbmonitor.external.notification.configs.redis;

import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.configs.redis.RedisAdapter;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationResponse;
import vn.com.mbbank.kanban.mbmonitor.external.notification.constans.RedisConstants;

/**
 * Component responsible for publishing notification messages to a Redis topic.
 * This class facilitates the communication of notification data to a Redis pub/sub channel
 * for subsequent processing or delivery by other subscribers.
 * The publication process leverages a {@link RedisAdapter} to interact with the RedisTemplate
 * and send the message to the defined topic.
 */
@Component
@RequiredArgsConstructor
public class NotificationRedisPublisher {
  private final RedisAdapter redisAdapter;

  /**
   * Publishes a notification message to the configured Redis topic for further processing or delivery.
   *
   * @param notification the notification data to be published, encapsulated
   *                     in a {@link NotificationResponse} object.
   */
  public void sendMessage(NotificationResponse notification) {
    redisAdapter.getRedisTemplate()
        .convertAndSend(RedisConstants.NOTIFICATION_REDIS_TOPIC, JSON.toJSONString(notification));
  }

}
