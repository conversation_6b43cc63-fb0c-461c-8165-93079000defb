package vn.com.mbbank.kanban.mbmonitor.external.notification.configs.redis;

import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.configs.redis.RedisAdapter;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationResponse;
import vn.com.mbbank.kanban.mbmonitor.external.notification.constans.RedisConstants;

@Component
@RequiredArgsConstructor
public class NotificationRedisPublisher {
  private final RedisAdapter redisAdapter;

  public void sendMessage(NotificationResponse notification) {
    redisAdapter.getRedisTemplate()
        .convertAndSend(RedisConstants.NOTIFICATION_REDIS_TOPIC, JSON.toJSONString(notification));
  }

}
