package vn.com.mbbank.kanban.mbmonitor.common.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.KanbanRedisService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsConfigModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsGroupChatModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsTokenModel;
import vn.com.mbbank.kanban.mbmonitor.common.services.RestApiService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.RedisUtils;

@ExtendWith(MockitoExtension.class)
/**
 * Generate by K-tool
 * Create date: 2025-05-05
 */
class CommonTeamsServiceImplTest {

  @Mock
  private KanbanRedisService kanbanRedisService;

  @Mock
  private RestApiService restApiService;

  @Spy
  @InjectMocks
  private CommonTeamsServiceImpl commonTeamsService;

  private TeamsConfigModel teamsConfig;

  @Mock
  private ObjectMapper objectMapper;

  @BeforeEach
  void setUp() {
    teamsConfig = new TeamsConfigModel();
    teamsConfig.setClientId("testClientId");
    teamsConfig.setClientSecret("testClientSecret");
    teamsConfig.setTenantId("testTenantId");
    teamsConfig.setEmail("<EMAIL>");
    teamsConfig.setPassword("testPassword");

    ReflectionTestUtils.setField(commonTeamsService, "teamsConfig", teamsConfig);
    ReflectionTestUtils.setField(commonTeamsService, "redisTokenKey",
        RedisUtils.getTeamsTokenRedisKey(teamsConfig.getClientId(), teamsConfig.getEmail()));
    ReflectionTestUtils.setField(commonTeamsService, "proxyTeamsIp", "127.0.0.1");
    ReflectionTestUtils.setField(commonTeamsService, "proxyTeamsPort", 8080);
  }


  @Test
  void getToken_TokenInRedis_ReturnsTokenFromRedis() {
    String expectedToken = "redisToken";
    when(kanbanRedisService.isExistsByKey(anyString())).thenReturn(true);
    when(kanbanRedisService.get(anyString(), eq(null), eq(String.class))).thenReturn(expectedToken);

    String actualToken = commonTeamsService.getToken();

    assertEquals(expectedToken, actualToken);
    verify(restApiService, never()).post(anyString(), any(), any(HttpHeaders.class), eq(TeamsTokenModel.class));
  }

  @Test
  void getToken_TokenNotInRedis_ReturnsTokenFromTeams() {
    TeamsTokenModel tokenModel = new TeamsTokenModel();
    tokenModel.setToken("teamsToken");
    tokenModel.setExpTime(3600L);
    when(kanbanRedisService.isExistsByKey(anyString())).thenReturn(false);
    doReturn(tokenModel).when(commonTeamsService).getTokenFromTeams();

    String actualToken = commonTeamsService.getToken();

    assertEquals("teamsToken", actualToken);
    verify(commonTeamsService).getTokenFromTeams();
  }

  @Test
  void getToken_TokenNotInRedis_GetTokenFromTeamsReturnsNull() {
    doReturn(false).when(kanbanRedisService).isExistsByKey(anyString());
    doReturn(null).when(commonTeamsService).getTokenFromTeams();

    String actualToken = commonTeamsService.getToken();

    assertNull(actualToken);
     verify(commonTeamsService).getTokenFromTeams();
    verify(kanbanRedisService, never()).save(anyString(), anyString(), anyLong());
  }

  @Test
  void getToken_ExceptionThrown_ReturnsNull() {
    when(kanbanRedisService.isExistsByKey(anyString())).thenThrow(new RuntimeException("Redis error"));

    String actualToken = commonTeamsService.getToken();

    assertNull(actualToken);
  }

  @Test
  void sendMessageWithBody_ValidInput_ReturnsResponseEntity() {
    String body = "{\"body\":{\"content\":\"Hello Teams!\",\"contentType\":\"text\"}}";
    String chatId = "testChatId";
    ResponseEntity<String> expectedResponse = ResponseEntity.ok("Message sent");
    String token = "testToken";
    when(commonTeamsService.getToken()).thenReturn(token);
    String endpoint = "https://graph.microsoft.com/v1.0/chats/" + chatId + "/messages";
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setBearerAuth(token);
    when(restApiService.post(endpoint, body, headers, String.class)).thenReturn(expectedResponse);

    ResponseEntity<String> actualResponse = commonTeamsService.sendMessageWithBody(body, chatId);

    assertEquals(expectedResponse, actualResponse);
    verify(restApiService).post(endpoint, body, headers, String.class);
  }

  @Test
  void sendMessageWithBody_TeamsConfigMissing_ReturnsInternalServerError() {
    ReflectionTestUtils.setField(commonTeamsService, "teamsConfig", null);
    String body = "{\"body\":{\"content\":\"Hello Teams!\",\"contentType\":\"text\"}}";
    String chatId = "testChatId";

    ResponseEntity<String> actualResponse = commonTeamsService.sendMessageWithBody(body, chatId);

    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, actualResponse.getStatusCode());
    assertTrue(actualResponse.getBody().contains("Teams configuration is missing"));
  }

  @Test
  void sendMessageWithBody_ExceptionThrown_ReturnsInternalServerError() {
    String body = "{\"body\":{\"content\":\"Hello Teams!\",\"contentType\":\"text\"}}";
    String chatId = "testChatId";
    when(commonTeamsService.getToken()).thenReturn("testToken");
    when(restApiService.post(anyString(), any(), any(HttpHeaders.class), eq(String.class))).thenThrow(new RuntimeException("API error"));

    ResponseEntity<String> actualResponse = commonTeamsService.sendMessageWithBody(body, chatId);

    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, actualResponse.getStatusCode());
    assertTrue(actualResponse.getBody().contains("Failed to send message"));
  }

  @Test
  void sendMessage_JsonProcessingException() throws JsonProcessingException {
    // Arrange
    String message = "Test message";
    String chatId = "testChatId";
    String errorMessage = "JSON processing error";

    // Mock ObjectMapper để ném JsonProcessingException
    when(objectMapper.writeValueAsString(any(ObjectNode.class)))
        .thenThrow(new JsonProcessingException(errorMessage) {});

    //getTokenFromTeams
    var teamsTokenModel = new TeamsTokenModel();
    teamsTokenModel.setToken("abc");
    teamsTokenModel.setExpTime(12L);
    when(commonTeamsService.getTokenFromTeams()).thenReturn(teamsTokenModel);


    // Arrange
    TeamsTokenModel tokenModel = new TeamsTokenModel();
    tokenModel.setToken("testAccessToken");
    tokenModel.setExpTime(1L);
    ResponseEntity<TeamsTokenModel> responseEntity = new ResponseEntity<>(tokenModel, HttpStatus.OK);

    // Mock restApiService.post
    when(restApiService.post(any(),any(), any(), eq(TeamsTokenModel.class)))
        .thenReturn(responseEntity);


    // Act
    ResponseEntity<String> response = commonTeamsService.sendMessage(message, chatId);

    // Assert
    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
  }
  @Test
  void createGroupChat_ValidInput_ReturnsTeamsGroupChatModel() throws BusinessException {
    List<String> users = Collections.singletonList("<EMAIL>");
    Boolean isSkipEmailInValid = false;
    String token = "testToken";
    when(commonTeamsService.getToken()).thenReturn(token);
    TeamsGroupChatModel expectedModel = new TeamsGroupChatModel();
    doReturn(expectedModel).when(commonTeamsService).createChatWithMembers(users, token, isSkipEmailInValid);

    TeamsGroupChatModel actualModel = commonTeamsService.createGroupChat(users, isSkipEmailInValid);

    assertEquals(expectedModel, actualModel);
    verify(commonTeamsService).createChatWithMembers(users, token, isSkipEmailInValid);
  }

  @Test
  void createGroupChat_TokenNull_ThrowsBusinessException() {
    List<String> users = Collections.singletonList("<EMAIL>");
    Boolean isSkipEmailInValid = false;
    when(commonTeamsService.getToken()).thenReturn(null);

    assertThrows(BusinessException.class, () -> commonTeamsService.createGroupChat(users, isSkipEmailInValid));
  }

  @Test
  void getTokenFromTeams_ValidConfig_ReturnsTeamsTokenModel() {
    TeamsTokenModel expectedTokenModel = new TeamsTokenModel();
    expectedTokenModel.setToken("testToken");
    ResponseEntity<TeamsTokenModel> responseEntity = new ResponseEntity<>(expectedTokenModel, HttpStatus.OK);
    when(restApiService.post(anyString(), any(), any(HttpHeaders.class), eq(TeamsTokenModel.class))).thenReturn(responseEntity);

    TeamsTokenModel actualTokenModel = commonTeamsService.getTokenFromTeams();

    assertEquals(expectedTokenModel, actualTokenModel);
    verify(restApiService).post(anyString(), any(), any(HttpHeaders.class), eq(TeamsTokenModel.class));
  }

  @Test
  void getTokenFromTeams_TeamsConfigMissing_ReturnsNull() {
    ReflectionTestUtils.setField(commonTeamsService, "teamsConfig", null);

    TeamsTokenModel actualTokenModel = commonTeamsService.getTokenFromTeams();

    assertNull(actualTokenModel);
  }

  @Test
  void getTokenFromTeams_InvalidCredentials_ReturnsNull() {
    ResponseEntity<TeamsTokenModel> responseEntity = new ResponseEntity<>(HttpStatus.BAD_REQUEST);
    when(restApiService.post(anyString(), any(), any(HttpHeaders.class), eq(TeamsTokenModel.class))).thenReturn(responseEntity);

    TeamsTokenModel actualTokenModel = commonTeamsService.getTokenFromTeams();

    assertNull(actualTokenModel);
  }

  @Test
  void getTokenFromTeams_ExceptionThrown_ReturnsNull() {
    when(restApiService.post(anyString(), any(), any(HttpHeaders.class), eq(TeamsTokenModel.class))).thenThrow(new RuntimeException("API error"));

    TeamsTokenModel actualTokenModel = commonTeamsService.getTokenFromTeams();

    assertNull(actualTokenModel);
  }

  @Test
  void createChatWithMembers_SuccessfulCreation() throws BusinessException {
    List<String> users = new ArrayList<>();
    users.add("<EMAIL>");
    String token = "testToken";
    Boolean isSkipEmailInValid = false;
    String graphApiUrl = "https://graph.microsoft.com/v1.0/chats";
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setBearerAuth(token);
    String responseBody = "{\"id\":\"testChatId\"}";
    when(commonTeamsService.getToken()).thenReturn("token");
    ResponseEntity<Object> response = new ResponseEntity<>(responseBody, HttpStatus.OK);
    when(restApiService.post(any(), any(), any(), any())).thenReturn(response);
    TeamsGroupChatModel result = commonTeamsService.createGroupChat(users, isSkipEmailInValid);
    assertEquals("testChatId", result.getGroupChatId());
  }

  @Test
  void createChatWithMembers_SkipInvalidEmailsAndSuccessfulCreation() throws BusinessException {
    List<String> users = new ArrayList<>();
    users.add("<EMAIL>");
    String token = "testToken";
    Boolean isSkipEmailInValid = true;
    String graphApiUrl = "https://graph.microsoft.com/v1.0/chats";
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setBearerAuth(token);
    String invalidResponse = "{\"error\":{\"details\":[{\"target\":\"members\",\"message\":\"Invalid email address\"}]}}";
    ResponseEntity<Object> initialResponse = new ResponseEntity<>(invalidResponse, HttpStatus.BAD_REQUEST);
    String validResponse = "{\"id\":\"testChatId\"}";
    ResponseEntity<Object> secondResponse = new ResponseEntity<>(validResponse, HttpStatus.OK);
    when(commonTeamsService.getToken()).thenReturn("token");
    when(restApiService.post(any(), any(), any(), any()))
        .thenReturn(initialResponse)
        .thenReturn(secondResponse);
    when(commonTeamsService.getToken()).thenReturn("token");
    TeamsGroupChatModel result = commonTeamsService.createGroupChat(users, isSkipEmailInValid);
    assertEquals("testChatId", result.getGroupChatId());
  }

  @Test
  void createChatWithMembers_SkipInvalidEmailsAndNoSuccess() throws BusinessException {
    List<String> users = new ArrayList<>();
    users.add("<EMAIL>");
    String token = "testToken";
    Boolean isSkipEmailInValid = true;
    String graphApiUrl = "https://graph.microsoft.com/v1.0/chats";
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setBearerAuth(token);
    String invalidResponse = "{\"error\":{\"details\":[{\"target\":\"members\",\"message\":\"Invalid email address\"}]}}";
    ResponseEntity<String> initialResponse = new ResponseEntity<>(invalidResponse, HttpStatus.BAD_REQUEST);
    ResponseEntity<String> secondResponse = new ResponseEntity<>(invalidResponse, HttpStatus.BAD_REQUEST);
    when(commonTeamsService.getToken())
        .thenReturn("token");

    when(restApiService.post(eq(graphApiUrl), anyString(), eq(headers), eq(String.class)))
        .thenReturn(initialResponse)
        .thenReturn(secondResponse);
    TeamsGroupChatModel result = commonTeamsService.createGroupChat(users, isSkipEmailInValid);
    assertEquals(0, result.getEmailValid().size());
  }

  @Test
  void createChatWithMembers_SkipInvalidEmailsAndFailedSecondAttempt() {
    List<String> users = new ArrayList<>();
    users.add("<EMAIL>");
    users.add("<EMAIL>");
    String token = "testToken";
    Boolean isSkipEmailInValid = true;
    String graphApiUrl = "https://graph.microsoft.com/v1.0/chats";
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setBearerAuth(token);
    String invalidResponse = "{\"error\":{\"details\":[{\"target\":\"members\",\"message\":\"Invalid email address\"}]}}";
    ResponseEntity<String> initialResponse = new ResponseEntity<>(invalidResponse, HttpStatus.BAD_REQUEST);
    ResponseEntity<String> secondResponse = new ResponseEntity<>(invalidResponse, HttpStatus.BAD_REQUEST);
    when(restApiService.post(eq(graphApiUrl), any(), eq(headers), eq(String.class)))
        .thenReturn(initialResponse)
        .thenReturn(secondResponse);
    //when(TeamsUtils.extractEmailsCreateGroupInvalid(invalidResponse)).thenReturn(Collections.singletonList("<EMAIL>"));

    TeamsGroupChatModel result = commonTeamsService.createChatWithMembers(users, token, isSkipEmailInValid);

    assertEquals(0, result.getEmailValid().size());
    assertEquals(Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>"), result.getEmailInvalid());
  }

  @Test
  void createChatWithMembers_NoSkipInvalidEmails_FailedFirstAttempt() {
    List<String> users = new ArrayList<>();
    users.add("<EMAIL>");
    users.add("<EMAIL>");
    String token = "testToken";
    Boolean isSkipEmailInValid = false;
    String graphApiUrl = "https://graph.microsoft.com/v1.0/chats";
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setBearerAuth(token);
    String invalidResponse = "{\"error\":{\"details\":[{\"target\":\"members\",\"message\":\"Invalid email address\"}]}}";
    ResponseEntity<String> failedResponse = new ResponseEntity<>(invalidResponse, HttpStatus.BAD_REQUEST);
    when(restApiService.post(eq(graphApiUrl), any(), eq(headers), eq(String.class)))
        .thenReturn(failedResponse);
    TeamsGroupChatModel result = commonTeamsService.createChatWithMembers(users, token, isSkipEmailInValid);

    assertEquals(List.of("<EMAIL>", "<EMAIL>"), result.getEmailValid());
  }


  @Test
  void getAllGroupChat_Success() throws Exception {
    String token = "testToken";
    TeamsGroupChatModel chatModel1 = new TeamsGroupChatModel();
    chatModel1.setGroupChatId("chat1");
    TeamsGroupChatModel chatModel2 = new TeamsGroupChatModel();
    chatModel2.setGroupChatId("chat2");
    List<TeamsGroupChatModel> expectedResult = new ArrayList<>();
    expectedResult.add(chatModel1);
    expectedResult.add(chatModel2);

    doNothing().when(commonTeamsService).fetchChatsRecursively(anyString(), anyList());

    List<TeamsGroupChatModel> actualResult = commonTeamsService.getAllGroupChat();

    assertNotNull(actualResult);
    verify(commonTeamsService).fetchChatsRecursively(eq("https://graph.microsoft.com/v1.0/chats?$filter=chatType eq 'group'&$expand=members&$top=50"), anyList());
  }

  @Test
  void getAllGroupChat_ExceptionThrown() throws Exception {
    doThrow(new Exception("API error")).when(commonTeamsService).fetchChatsRecursively(anyString(), anyList());

    assertThrows(RuntimeException.class, () -> commonTeamsService.getAllGroupChat());
    verify(commonTeamsService).fetchChatsRecursively(eq("https://graph.microsoft.com/v1.0/chats?$filter=chatType eq 'group'&$expand=members&$top=50"), anyList());
  }

  @Test
  void fetchChatsRecursively_Success() throws Exception {
    String url = "https://graph.microsoft.com/v1.0/chats?$filter=chatType eq 'group'&$expand=members&$top=50";
    String token = "testToken";
    List<TeamsGroupChatModel> result = new ArrayList<>();

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setBearerAuth(token);

    String responseBody = "{\"value\": [{\"id\": \"chat1\", \"topic\": \"Topic 1\", \"chatType\": \"group\", \"members\": []}]}";
    ResponseEntity<String> response = new ResponseEntity<>(responseBody, HttpStatus.OK);

    doReturn(token).when(commonTeamsService).getToken();
    when(restApiService.get(anyString(), any(HttpHeaders.class), eq(String.class))).thenReturn(response);

    commonTeamsService.fetchChatsRecursively(url, result);

    assertEquals(1, result.size());
    assertEquals("chat1", result.get(0).getGroupChatId());
    verify(commonTeamsService).getToken();
    verify(restApiService).get(url, headers, String.class);
  }

  @Test
  void fetchChatsRecursively_NextLink() throws Exception {
    String url = "https://graph.microsoft.com/v1.0/chats?$filter=chatType eq 'group'&$expand=members&$top=50";
    String token = "testToken";
    List<TeamsGroupChatModel> result = new ArrayList<>();

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setBearerAuth(token);

    String responseBody = "{\"value\": [{\"id\": \"chat1\", \"topic\": \"Topic 1\", \"chatType\": \"group\", \"members\": []}]}";
    ResponseEntity<String> response = new ResponseEntity<>(responseBody, HttpStatus.OK);

    doReturn(token).when(commonTeamsService).getToken();
    when(restApiService.get(anyString(), any(HttpHeaders.class), eq(String.class))).thenReturn(response);
    commonTeamsService.fetchChatsRecursively(url, result);

    assertEquals(1, result.size());
    assertEquals("chat1", result.get(0).getGroupChatId());
    verify(commonTeamsService).getToken();
    verify(restApiService).get(url, headers, String.class);
  }

  @Test
  void fetchChatsRecursively_EmptyChatType() throws Exception {
    String url = "https://graph.microsoft.com/v1.0/chats?$filter=chatType eq 'group'&$expand=members&$top=50";
    String token = "testToken";
    List<TeamsGroupChatModel> result = new ArrayList<>();

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setBearerAuth(token);

    String responseBody = "{\"value\": [{\"id\": \"chat1\", \"topic\": \"Topic 1\", \"chatType\": \"\", \"members\": []}]}";
    ResponseEntity<String> response = new ResponseEntity<>(responseBody, HttpStatus.OK);

    doReturn(token).when(commonTeamsService).getToken();
    when(restApiService.get(anyString(), any(HttpHeaders.class), eq(String.class))).thenReturn(response);

    commonTeamsService.fetchChatsRecursively(url, result);

    assertEquals(0, result.size());
    verify(commonTeamsService).getToken();
    verify(restApiService).get(url, headers, String.class);
  }

  @Test
  void fetchChatsRecursively_Non2xxResponse() throws Exception {
    String url = "https://graph.microsoft.com/v1.0/chats?$filter=chatType eq 'group'&$expand=members&$top=50";
    String token = "testToken";
    List<TeamsGroupChatModel> result = new ArrayList<>();

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setBearerAuth(token);

    ResponseEntity<String> response = new ResponseEntity<>("Error", HttpStatus.BAD_REQUEST);

    doReturn(token).when(commonTeamsService).getToken();
    when(restApiService.get(anyString(), any(HttpHeaders.class), eq(String.class))).thenReturn(response);

    assertThrows(BusinessException.class, () -> commonTeamsService.fetchChatsRecursively(url, result));
    verify(commonTeamsService).getToken();
    verify(restApiService).get(url, headers, String.class);
  }

  @Test
  void fetchChatsRecursively_Value_not_array() throws Exception {
    String url = "https://graph.microsoft.com/v1.0/chats?$filter=chatType eq 'group'&$expand=members&$top=50";
    String token = "testToken";
    List<TeamsGroupChatModel> result = new ArrayList<>();

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setBearerAuth(token);

    String responseBody = "{\"value\": \"a\"}";
    ResponseEntity<String> response = new ResponseEntity<>(responseBody, HttpStatus.OK);

    doReturn(token).when(commonTeamsService).getToken();
    when(restApiService.get(anyString(), any(HttpHeaders.class), eq(String.class))).thenReturn(response);

    commonTeamsService.fetchChatsRecursively(url, result);

    assertEquals(0, result.size());
    verify(commonTeamsService).getToken();
    verify(restApiService).get(url, headers, String.class);
  }

  @Test
  void fetchChatsRecursively_WithValidMembersAndEmails() throws Exception {
    String url = "https://graph.microsoft.com/v1.0/chats?$filter=chatType eq 'group'&$expand=members&$top=50";
    String token = "testToken";
    List<TeamsGroupChatModel> result = new ArrayList<>();

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setBearerAuth(token);

    // JSON response với chat và members có email hợp lệ
    String responseBody = "{\"value\": [{\"id\": \"chat1\", \"topic\": \"Topic 1\", \"chatType\": \"group\", \"members\": [{\"email\": \"<EMAIL>\"}, {\"email\": \"<EMAIL>\"}]}]}";
    ResponseEntity<String> response = new ResponseEntity<>(responseBody, HttpStatus.OK);

    // Mock dependencies
    doReturn(token).when(commonTeamsService).getToken();
    when(restApiService.get(anyString(), any(HttpHeaders.class), eq(String.class))).thenReturn(response);

    // Chạy phương thức
    commonTeamsService.fetchChatsRecursively(url, result);

    // Kiểm tra kết quả
    assertEquals(1, result.size());
    TeamsGroupChatModel chatModel = result.get(0);
    assertEquals("chat1", chatModel.getGroupChatId());
    assertEquals("Topic 1", chatModel.getGroupChatName());
    assertEquals(2, chatModel.getEmailValid().size());
    assertTrue(chatModel.getEmailValid().contains("<EMAIL>"));
    assertTrue(chatModel.getEmailValid().contains("<EMAIL>"));
    assertEquals(0, chatModel.getEmailInvalid().size());

    // Verify các phương thức được gọi
    verify(commonTeamsService).getToken();
    verify(restApiService).get(url, headers, String.class);
  }

  @Test
  void fetchChatsRecursively_SkipsMembersWithNullOrEmptyEmails() throws Exception {
    String url = "https://graph.microsoft.com/v1.0/chats?$filter=chatType eq 'group'&$expand=members&$top=50";
    String token = "testToken";
    List<TeamsGroupChatModel> result = new ArrayList<>();

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setBearerAuth(token);

    // JSON response với chat có members, một số email null hoặc rỗng
    String responseBody = "{\"value\": [{\"id\": \"chat1\", \"topic\": \"Topic 1\", \"chatType\": \"group\", \"members\": [{\"email\": \"<EMAIL>\"}, {\"email\": \"\"}, {\"email\": null}]}]}";
    ResponseEntity<String> response = new ResponseEntity<>(responseBody, HttpStatus.OK);

    // Mock dependencies
    doReturn(token).when(commonTeamsService).getToken();
    when(restApiService.get(anyString(), any(HttpHeaders.class), eq(String.class))).thenReturn(response);

    // Chạy phương thức
    commonTeamsService.fetchChatsRecursively(url, result);

    // Kiểm tra kết quả
    assertEquals(1, result.size());
    TeamsGroupChatModel chatModel = result.get(0);
    assertEquals("chat1", chatModel.getGroupChatId());
    assertEquals("Topic 1", chatModel.getGroupChatName());
    assertEquals(1, chatModel.getEmailValid().size()); // Chỉ có 1 email hợp lệ
    assertTrue(chatModel.getEmailValid().contains("<EMAIL>"));
    assertEquals(0, chatModel.getEmailInvalid().size());

    // Verify các phương thức được gọi
    verify(commonTeamsService).getToken();
    verify(restApiService).get(url, headers, String.class);
  }
}