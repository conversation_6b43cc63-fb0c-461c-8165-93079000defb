package vn.com.mbbank.kanban.mbmonitor.common.configs;

import org.springframework.context.annotation.Configuration;
import vn.com.mbbank.kanban.core.configs.KanbanEntityListener;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/23/2025
 */
@Configuration
public class EntityListenerImpl implements KanbanEntityListener {
  @Override
  public void prePersist(BaseEntity entity) {
    if (entity.getClass().isAnnotationPresent(KanbanAutoGenerateUlId.class)
        &&
        KanbanCommonUtil.isEmpty(entity.getId())) {
      entity.setId(GeneratorUtil.generateId());
    }
  }
}
