package vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls;

import vn.com.mbbank.kanban.mbmonitor.common.configs.KanbanPropertyConfigUtils;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.BaseUrl;

/**
 * External notification URL.
 */
public class ExternalNotificationUrl extends BaseUrl {
  public static final String VERSION = "/v1";
  public static final String NOTIFICATION = VERSION + "/notifications";

  /**
   * Demo for ExternalNotificationUrl.
   *
   * @param path Path
   */
  public ExternalNotificationUrl(String path) {
    super(path);
  }

  /**
   * Load url from properties.
   *
   * @param baseApi baseApi
   * @return url full
   */
  public static String getUrl(String baseApi) {
    return KanbanPropertyConfigUtils.getProperty("monitor.external.notification.url",
        getBaseUrl() + "api/external-notification") + baseApi;
  }

}
