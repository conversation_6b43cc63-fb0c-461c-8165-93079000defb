package vn.com.mbbank.kanban.mbmonitor.external.notification.services;

import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEntity;
import vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request.NotificationCursor;
import vn.com.mbbank.kanban.mbmonitor.external.notification.dtos.request.NotificationSearchRequest;

/**
 * interface logic NotificationService.
 */
public interface NotificationService extends BaseService<NotificationEntity, String> {


  /**
   * Saves a notification and returns the corresponding response.
   *
   * @param notification the NotificationRequest object containing notification details to be saved
   * @return a NotificationResponse object representing the saved notification
   */
  NotificationResponse save(NotificationRequest notification);

  /**
   * Retrieves the total number of unread messages for a specific user.
   *
   * @param userName the username for which the unread message count is to be fetched
   * @return the total number of unread messages as a Long
   */
  Long getUnreadCount(String userName);

  /**
   * Marks a specific notification as read by its ID.
   *
   * @param id the unique identifier of the notification to be marked as read
   * @throws BusinessException if an error occurs during the operation
   */
  void markAsRead(String id) throws BusinessException;

  /**
   * Retrieves a paginated list of notifications based on the search criteria specified in the given
   * NotificationSearchRequest, including filters, pagination, and cursor-based navigation details.
   *
   * @param searchRequest the search request containing filters
   * @return page of notification
   */
  CursorPageResponse<NotificationResponse, NotificationCursor> findAllWithPaging(
      NotificationSearchRequest searchRequest);
}
